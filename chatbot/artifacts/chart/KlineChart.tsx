import React from 'react';
import {
    <PERSON><PERSON>sed<PERSON>hart,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Bar,
    ReferenceLine,
    Customized,
} from 'recharts';

interface KlineDataItem {
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}

interface ChartData {
    kline_data?: KlineDataItem[];
    symbol?: string;
    price?: number;
    volume_24h?: number;
    change_24h?: number;
    change_24h_percent?: number;
    high_24h?: number;
    low_24h?: number;
    analysis?: string;
    chart_suggestion?: string;
}

interface KlineChartProps {
    data: ChartData;
    chartType: 'kline' | 'line' | 'bar';
}

// 格式化时间戳为可读时间
const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化价格
const formatPrice = (value: number) => {
    return `$${value.toLocaleString('en-US', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
    })}`;
};

// 格式化成交量
const formatVolume = (value: number) => {
    if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
};

// 自定义K线柱状图组件
const CandlestickBar = (props: any) => {
    const { payload, x, y, width, height } = props;
    if (!payload) return null;
    const { open, high, low, close } = payload;
    const isRising = close >= open;
    const color = isRising ? '#10b981' : '#ef4444'; // 绿色上涨，红色下跌
    // 计算相对位置
    const range = high - low;
    if (range === 0) return null;
    const bodyTopRatio = (Math.max(open, close) - low) / range;
    const bodyBottomRatio = (Math.min(open, close) - low) / range;
    const wickTopRatio = (high - low) / range;
    const wickBottomRatio = (low - low) / range;
    const bodyTopY = y + height * (1 - bodyTopRatio);
    const bodyBottomY = y + height * (1 - bodyBottomRatio);
    const bodyActualHeight = Math.abs(bodyTopY - bodyBottomY);
    return (
        <g>
            {/* 上影线 */}
            <line
                x1={x + width / 2}
                y1={y}
                x2={x + width / 2}
                y2={bodyTopY}
                stroke={color}
                strokeWidth="1"
            />
            {/* 下影线 */}
            <line
                x1={x + width / 2}
                y1={bodyBottomY}
                x2={x + width / 2}
                y2={y + height}
                stroke={color}
                strokeWidth="1"
            />
            {/* K线实体 */}
            <rect
                x={x + width * 0.2}
                y={Math.min(bodyTopY, bodyBottomY)}
                width={width * 0.6}
                height={Math.max(bodyActualHeight, 1)}
                fill={color}
                stroke={color}
                strokeWidth="1"
            />
        </g>
    );
};

// 自定义工具提示
const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
        const data = payload[0].payload;
        const isRising = data.close >= data.open;
        
        return (
            <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-md">
                <p className="font-medium text-gray-900 mb-2">
                    {formatTime(data.timestamp)}
                </p>
                <div className="space-y-1 text-sm">
                    <p className="flex justify-between">
                        <span className="text-gray-600">开盘:</span>
                        <span className="font-medium text-gray-900">{formatPrice(data.open)}</span>
                    </p>
                    <p className="flex justify-between">
                        <span className="text-gray-600">最高:</span>
                        <span className="font-medium text-green-600">{formatPrice(data.high)}</span>
                    </p>
                    <p className="flex justify-between">
                        <span className="text-gray-600">最低:</span>
                        <span className="font-medium text-red-600">{formatPrice(data.low)}</span>
                    </p>
                    <p className="flex justify-between">
                        <span className="text-gray-600">收盘:</span>
                        <span className={`font-medium ${isRising ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPrice(data.close)}
                        </span>
                    </p>
                    <p className="flex justify-between">
                        <span className="text-gray-600">成交量:</span>
                        <span className="font-medium text-gray-900">{formatVolume(data.volume)}</span>
                    </p>
                    <p className="flex justify-between">
                        <span className="text-gray-600">涨跌:</span>
                        <span className={`font-medium ${isRising ? 'text-green-600' : 'text-red-600'}`}>
                            {isRising ? '+' : ''}{((data.close - data.open) / data.open * 100).toFixed(2)}%
                        </span>
                    </p>
                </div>
            </div>
        );
    }
    return null;
};

export const KlineChart: React.FC<KlineChartProps> = ({ data }) => {
    const { kline_data, symbol, price, change_24h_percent, analysis } = data;
    
    if (!kline_data || kline_data.length === 0) {
        return (
            <div className="flex items-center justify-center h-96 text-gray-500">
                <p>暂无K线数据</p>
            </div>
        );
    }
    
    // 数据验证和清理
    const validKlineData = kline_data.filter(item => 
        item && 
        typeof item.timestamp === 'number' &&
        [item.open, item.high, item.low, item.close, item.volume].every(val => 
            typeof val === 'number' && !isNaN(val) && isFinite(val)
        )
    );
    
    if (validKlineData.length === 0) {
        return (
            <div className="flex items-center justify-center h-96 text-gray-500">
                <p>无有效K线数据</p>
            </div>
        );
    }
    
    // 转换数据格式并排序
    const chartData = validKlineData
        .map(item => ({
            ...item,
            time: formatTime(item.timestamp),
        }))
        .sort((a, b) => a.timestamp - b.timestamp);
    
    // 修复Y轴域计算
    const allPrices = validKlineData.flatMap(item => [item.open, item.high, item.low, item.close]);
    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    const priceRange = maxPrice - minPrice;
    
    // 确保有合理的Y轴范围，避免除零和负数问题
    const padding = Math.max(priceRange * 0.05, maxPrice * 0.001); // 最小5%或0.1%的padding
    const yAxisMin = Math.max(0, minPrice - padding); // 确保不为负数
    const yAxisMax = maxPrice + padding;
    
    return (
        <div className="w-full space-y-4">
            {/* 头部信息 */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                    <h3 className="text-lg font-bold text-gray-900">
                        {symbol || 'K线图表'}
                    </h3>
                    {price && (
                        <span className="text-xl font-bold text-gray-900">
                            {formatPrice(price)}
                        </span>
                    )}
                    {change_24h_percent !== undefined && (
                        <span className={`px-2 py-1 rounded text-sm font-medium ${
                            change_24h_percent >= 0 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                        }`}>
                            {change_24h_percent >= 0 ? '+' : ''}{change_24h_percent.toFixed(2)}%
                        </span>
                    )}
                </div>
                <div className="text-sm text-gray-600 mt-2 md:mt-0">
                    数据点: {validKlineData.length} | 时间范围: 1小时
                </div>
            </div>
            
            {/* K线图 */}
            <div className="w-full" style={{ height: '500px' }}>
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                            dataKey="time"
                            tick={{ fontSize: 12, fill: '#374151' }}
                            interval={Math.floor(chartData.length / 8)}
                        />
                        <YAxis 
                            domain={[yAxisMin, yAxisMax]}
                            tick={{ fontSize: 12, fill: '#374151' }}
                            tickFormatter={formatPrice}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Bar 
                            dataKey="close" 
                            shape={<CandlestickBar />}
                            isAnimationActive={true}
                        />
                        {/* 添加当前价格参考线 */}
                        {price && (
                            <ReferenceLine 
                                y={price} 
                                stroke="#6366f1" 
                                strokeDasharray="5 5"
                                label={{ value: `当前价格: ${formatPrice(price)}`, position: "top" }}
                            />
                        )}
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
            
            {/* 成交量图 */}
            <div className="w-full" style={{ height: '150px' }}>
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={chartData} margin={{ top: 10, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                            dataKey="time"
                            tick={{ fontSize: 10, fill: '#374151' }}
                            interval={Math.floor(chartData.length / 8)}
                        />
                        <YAxis 
                            tick={{ fontSize: 10, fill: '#374151' }}
                            tickFormatter={formatVolume}
                        />
                        <Tooltip 
                            formatter={(value: number) => [formatVolume(value), '成交量']}
                            labelFormatter={(label) => `时间: ${label}`}
                        />
                        <Bar 
                            dataKey="volume" 
                            fill="#8884d8"
                            opacity={0.6}
                            isAnimationActive={true}
                        />
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
            
            {/* 分析信息 */}
            {analysis && (
                <div className="p-4 bg-blue-50 dark:bg-gray-800 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-gray-100 mb-2">市场分析</h4>
                    <p className="text-blue-800 dark:text-gray-100 text-sm leading-relaxed">{analysis}</p>
                </div>
            )}
        </div>
    );
}; 