import React from 'react';
import { Artifact } from '@/components/artifact/CreateArtifact';
import { DocumentSkeleton } from '@/components/document/DocumentSkeleton';
import {
    CopyIcon,
    DownloadIcon,
    FullscreenIcon,
    LineChartIcon,
} from '@/components/Icons';
import { K<PERSON><PERSON>hart } from './KlineChart';
import { toast } from 'sonner';

interface ChartArtifactMetadata {
    chartType: 'kline' | 'line' | 'bar';
    symbol?: string;
    timeRange?: string;
}

interface KlineDataItem {
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}

interface ChartData {
    kline_data?: KlineDataItem[];
    symbol?: string;
    price?: number;
    volume_24h?: number;
    change_24h?: number;
    change_24h_percent?: number;
    high_24h?: number;
    low_24h?: number;
    analysis?: string;
    chart_suggestion?: string;
}

export const chartArtifact = new Artifact<'chart', ChartArtifactMetadata>({
    kind: 'chart',
    description: 'Useful for displaying financial charts like K-line, candlestick charts.',
    initialize: async ({ setMetadata }) => {
        setMetadata({
            chartType: 'kline',
        });
    },
    onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
        if (streamPart.type === 'chart-delta') {
            setArtifact((draftArtifact) => {
                return {
                    ...draftArtifact,
                    content: draftArtifact.content + (streamPart.content as string),
                    isVisible: true,
                    status: 'streaming',
                };
            });
        }
    },
    content: ({
        content,
        status,
        isLoading,
        metadata,
    }) => {
        if (isLoading) {
            return <DocumentSkeleton artifactKind="chart" />;
        }

        let chartData: ChartData;
        try {
            chartData = JSON.parse(content);
        } catch (error) {
            return (
                <div className="flex items-center justify-center h-96 text-gray-500 dark:text-gray-400">
                    <p>无法解析图表数据</p>
                </div>
            );
        }

        return (
            <div className="p-4 md:p-8">
                <KlineChart 
                    data={chartData}
                    chartType={metadata?.chartType || 'kline'}
                />
            </div>
        );
    },
    actions: [
        {
            icon: <CopyIcon size={18} />,
            description: '复制图表数据',
            onClick: ({ content }) => {
                navigator.clipboard.writeText(content);
                toast.success('图表数据已复制到剪贴板！');
            },
        },
        {
            icon: <DownloadIcon size={18} />,
            description: '下载图表',
            onClick: ({ content }) => {
                try {
                    const chartData = JSON.parse(content);
                    const dataStr = JSON.stringify(chartData, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `chart-data-${chartData.symbol || 'data'}-${Date.now()}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    toast.success('图表数据已下载！');
                } catch (error) {
                    toast.error('下载失败');
                }
            },
        },
        {
            icon: <FullscreenIcon size={18} />,
            description: '全屏查看',
            onClick: () => {
                toast.info('全屏功能开发中...');
            },
        },
    ],
    toolbar: [
        {
            icon: <LineChartIcon />,
            description: '分析图表趋势',
            onClick: ({ appendMessage }) => {
                appendMessage({
                    role: 'user',
                    content: '请帮我分析一下这个K线图的走势和趋势，给出投资建议。',
                });
            },
        },
    ],
}); 