# ---- 阶段 1: 使用预构建的依赖基础镜像 ----
FROM coinflow/bun-base AS deps

# ---- 阶段 2: 构建 ----
# Stage 1: Build application from source
FROM coinflow/bun-base as builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN bun run build

# Stage 2: Production image
FROM coinflow/bun-base
WORKDIR /app
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=deps /app/node_modules ./node_modules
CMD ["bun", "run", "start"]
