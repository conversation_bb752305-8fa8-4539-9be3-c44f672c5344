import type { Attachment } from 'ai';

import { LoaderIcon, FileIcon, PdfIcon, MdIcon, TxtIcon } from '@/components/Icons';
import { X } from 'lucide-react';
import cx from 'classnames';

// 新增：文件类型图标选择函数
function getFileIconComponent(name: string = '', contentType: string = '', size: number = 32) {
  const ext = name.split('.').pop()?.toLowerCase();
  if (ext === 'pdf' || contentType === 'application/pdf') return <PdfIcon size={size} />;
  if (ext === 'md' || contentType === 'text/markdown') return <MdIcon size={size} />;
  if (ext === 'txt' || contentType === 'text/plain') return <TxtIcon size={size} />;
  return <FileIcon size={size} />;
}

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
  onDelete,
}: {
  attachment: Attachment;
  isUploading?: boolean;
  onDelete?: () => void;
}) => {
  const { name, url, contentType } = attachment;

  return (
    <div data-testid="input-attachment-preview" className="flex flex-col gap-2">
      <div className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center overflow-hidden">
        {contentType ? (
          contentType.startsWith('image') ? (
            <>
              {/* 图片预览 */}
              <img
                key={url}
                src={url}
                alt={name ?? 'An image attachment'}
                className="rounded-md size-full object-cover"
              />
              {/* 删除按钮 */}
              {onDelete && !isUploading && (
                <button
                  type="button"
                  className={cx(
                    'absolute top-1 right-1 z-20 p-0.5 rounded-full bg-black/60 hover:bg-black/80 text-white',
                    'transition-colors duration-150'
                  )}
                  onClick={onDelete}
                  tabIndex={-1}
                  aria-label="删除图片"
                >
                  <X size={14} />
                </button>
              )}
            </>
          ) : (
            // 非图片类型显示文件图标
            <div className="flex flex-col items-center justify-center w-full h-full">
              <span className="text-zinc-400">
                {getFileIconComponent(name, contentType, 32)}
              </span>
            </div>
          )
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-full">
            <span className="text-zinc-400">
              <FileIcon size={32} />
            </span>
          </div>
        )}

        {isUploading && (
          <div
            data-testid="input-attachment-loader"
            className="animate-spin absolute text-zinc-500"
          >
            <LoaderIcon />
          </div>
        )}
      </div>
      <div className="text-xs text-zinc-500 max-w-16 truncate">{name}</div>
    </div>
  );
};
