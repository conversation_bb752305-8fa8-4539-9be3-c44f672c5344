{"name": "@ai-server/chatbot", "version": "3.0.23", "private": true, "packageManager": "bun@1.0.0", "scripts": {"dev": "bun scripts/start-chatbot-if-needed.ts", "build": "bunx next build", "start": "bunx next start", "lint": "bunx next lint", "lint:fix": "bunx next lint --fix"}, "dependencies": {"@ai-sdk/anthropic": "^1.0.4", "@ai-sdk/google": "^1.0.8", "@ai-sdk/openai": "^1.0.8", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.0.4", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.4.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.34.1", "@hookform/resolvers": "^3.10.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/instrumentation": "^0.56.0", "@opentelemetry/resources": "^1.28.0", "@opentelemetry/sdk-logs": "^0.56.0", "@opentelemetry/sdk-metrics": "^1.28.0", "@opentelemetry/sdk-trace-base": "^1.28.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@tailwindcss/typography": "^0.5.15", "@vercel/otel": "^1.12.1", "ai": "^4.0.34", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "codemirror": "^6.0.1", "d3-scale": "^4.0.2", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.18.0", "geist": "^1.3.1", "i18next": "^24.1.0", "i18next-browser-languagedetector": "^8.0.1", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.468.0", "mermaid": "^10.6.0", "next": "15.3.4", "next-auth": "4.24.11", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.24.1", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "react": "^18.3.1", "react-data-grid": "^7.0.0-beta.47", "react-dom": "^18.3.1", "react-hook-form": "^7.54.0", "react-i18next": "^15.2.0", "react-icons": "^5.4.0", "react-markdown": "^9.0.1", "recharts": "^2.13.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "zod": "^3.24.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.50.1", "@types/d3-scale": "^4.0.8", "@types/node": "^24.0.0", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^15.3.4", "postcss": "^8.5.4", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "typescript": "^5.8.3"}}