{"name": "ai-agent-scripts", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "./scripts/dev.sh", "dev:mock": "concurrently \"cd api && NODE_ENV=mock bun run dev\" \"cd chatbot && bun run dev\"", "dev:api": "cd api && bun run dev", "dev:chatbot": "cd chatbot && bun run dev", "build": "bun run build:api && bun run build:chatbot", "build:api": "cd api && bun run build", "build:chatbot": "cd chatbot && bun run build", "lint": "bun run lint:api && bun run lint:chatbot", "lint:api": "cd api && bun run lint", "lint:chatbot": "cd chatbot && bun run lint", "format": "bun run format:api && bun run format:chatbot", "format:api": "cd api && bun run format", "format:chatbot": "cd chatbot && bun run format", "test": "jest", "test:api": "cd api && bun run test", "test:integration": "jest tests/", "test:e2e": "playwright test", "e2e:headed": "playwright test --headed", "e2e:debug": "PWDEBUG=1 playwright test", "clean": "rm -rf node_modules api/node_modules chatbot/node_modules bun.lockb", "clean:workspaces": "rm -rf api/node_modules chatbot/node_modules", "clean:dev": "pkill -f 'tsx scripts/start-' || true; lsof -ti:3000,3001 | xargs kill -9 2>/dev/null || true; rm -f api/.nest-dev.pid chatbot/.nextjs-dev.pid", "install:all": "bun install && cd api && bun install && cd ../chatbot && bun install", "install:clean": "bun run clean && bun install && cd api && bun install && cd ../chatbot && bun install"}, "devDependencies": {"@nestjs/common": "^10.3.0", "@nestjs/config": "^4.0.2", "@playwright/test": "^1.53.2", "@types/jest": "^30.0.0", "@types/node-fetch": "^2", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "concurrently": "^8.2.2", "dotenv": "^17.0.1", "form-data": "^4.0.3", "jest": "^29.7.0", "jest-environment-node": "^30.0.4", "jest-junit": "^16.0.0", "jest-util": "^29", "node-fetch": "^2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ts-jest": "^29.1.2", "tsx": "^4.20.1", "typescript": "^5.8.3", "uuid": "^11.1.0"}, "dependencies": {"i18next-http-backend": "^3.0.2"}, "packageManager": "bun@1.0.0"}