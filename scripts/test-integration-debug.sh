#!/bin/bash

# 集成测试调试脚本
# 用于本地排查 CI 中的集成测试问题

echo "🔍 集成测试环境调试脚本"
echo "================================"

# 检查基本环境
echo "📍 基本环境信息:"
echo "当前工作目录: $(pwd)"
echo "Node.js 版本: $(node --version)"
echo "Yarn 版本: $(yarn --version)"
echo "NODE_ENV: ${NODE_ENV:-未设置}"

# 检查 Docker 环境
echo -e "\n🐳 Docker 环境检查:"
docker --version || echo "❌ Docker 未安装或不可用"
docker-compose --version || echo "❌ Docker Compose 未安装或不可用"

# 检查端口占用
echo -e "\n🔌 端口占用检查:"
lsof -i :3000 || echo "✅ 端口 3000 未被占用"
lsof -i :3001 || echo "✅ 端口 3001 未被占用"
lsof -i :5432 || echo "✅ 端口 5432 未被占用"
lsof -i :5002 || echo "✅ 端口 5002 未被占用"
lsof -i :18080 || echo "✅ 端口 18080 未被占用"

# 检查 docker-compose.yml 文件
echo -e "\n📄 Docker Compose 文件检查:"
if [ -f "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml 文件存在"
    echo "文件大小: $(stat -f%z docker-compose.yml) bytes"
    echo "最后修改: $(stat -f%Sm docker-compose.yml)"
else
    echo "❌ docker-compose.yml 文件不存在"
fi

# 检查 Jest 配置
echo -e "\n🧪 Jest 配置检查:"
if [ -f "jest.config.cjs" ]; then
    echo "✅ jest.config.cjs 文件存在"
    echo "globalSetup: $(grep -o 'globalSetup.*' jest.config.cjs | head -1)"
    echo "globalTeardown: $(grep -o 'globalTeardown.*' jest.config.cjs | head -1)"
else
    echo "❌ jest.config.cjs 文件不存在"
fi

# 检查测试环境文件
echo -e "\n📁 测试环境文件检查:"
test_files=(
    "tests/environment/testSetup.ts"
    "tests/environment/teardown.ts"
    "tests/environment/testUtils.ts"
)

for file in "${test_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done

# 尝试运行 docker-compose config
echo -e "\n🔧 Docker Compose 配置验证:"
if docker-compose config > /dev/null 2>&1; then
    echo "✅ docker-compose.yml 配置语法正确"
else
    echo "❌ docker-compose.yml 配置有误:"
    docker-compose config
fi

# 检查当前 Docker 容器状态
echo -e "\n📦 当前 Docker 容器状态:"
docker ps -a | grep -E "(postgres|openai-mock|searxng)" || echo "无相关容器运行"

# 提供运行建议
echo -e "\n💡 调试建议:"
echo "1. 如果端口被占用，请运行: yarn clean:dev"
echo "2. 如果 Docker 配置有误，请检查 docker-compose.yml"
echo "3. 如果要手动运行测试，请使用: yarn test --verbose --runInBand --no-cache"
echo "4. 如果要查看详细日志，请设置: DEBUG=* yarn test"

echo -e "\n🚀 准备运行集成测试..."
echo "命令: yarn test --verbose --runInBand --no-cache"
echo "================================"
