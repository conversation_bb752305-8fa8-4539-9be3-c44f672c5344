#!/bin/bash

# 设置进程组
set -m

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 清理函数
cleanup() {
    echo -e "\n${RED}🛑 Stopping all development servers...${NC}"

    # 杀死所有子进程
    pkill -P $$ 2>/dev/null || true

    # 清理端口
    echo -e "${YELLOW}🧹 Cleaning up ports 3000 and 3001...${NC}"
    lsof -ti:3000,3001 | xargs kill -9 2>/dev/null || true

    echo -e "${GREEN}✅ All processes cleaned up${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM EXIT

echo -e "${CYAN}🚀 Starting AI Agent development environment...${NC}"
echo -e "${BLUE}📍 API Server: http://localhost:3001${NC}"
echo -e "${BLUE}📍 Web Server: http://localhost:3000${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop all servers${NC}"
echo ""

# 启动API服务器
echo -e "${PURPLE}[api]${NC} 📡 Starting API server..."
(cd api && bun run dev 2>&1 | sed 's/^/[api] /') &
API_PID=$!

# 等待一下让API服务器先启动
sleep 3

# 启动Chatbot服务器
echo -e "${CYAN}[chatbot]${NC} 💬 Starting Chatbot server..."
(cd chatbot && bun run dev 2>&1 | sed 's/^/[chatbot] /') &
CHATBOT_PID=$!

echo ""
echo -e "${GREEN}🎉 Development environment is starting up...${NC}"
echo -e "${YELLOW}📋 Logs will appear below:${NC}"
echo ""

# 检查进程状态
check_processes() {
    local count=0
    while [ $count -lt 30 ]; do
        if ! kill -0 $API_PID 2>/dev/null || ! kill -0 $CHATBOT_PID 2>/dev/null; then
            echo -e "${RED}❌ One or more processes exited unexpectedly${NC}"
            return 1
        fi

        # 检查端口是否可用
        if nc -z localhost 3001 2>/dev/null && nc -z localhost 3000 2>/dev/null; then
            echo -e "${GREEN}✅ Both servers are running!${NC}"
            break
        fi

        sleep 2
        count=$((count + 1))
    done

    if [ $count -eq 30 ]; then
        echo -e "${YELLOW}⚠️  Servers taking longer than expected to start...${NC}"
    fi
}

# 启动检查
check_processes &

# 等待所有后台作业
wait
