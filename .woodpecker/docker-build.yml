# Woodpecker CI - Docker Build Pipeline

when:
  - event: manual

steps:
  - name: build-api
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/search-api
      dockerfile: api/Dockerfile
      context: api/
      platforms:
        - linux/amd64
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_to: type=inline

  - name: build-web
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/agent-web
      dockerfile: chatbot/Dockerfile
      context: chatbot/
      platforms:
        - linux/amd64
      tags:
        - latest
      username:
        from_secret: DOCKERH<PERSON><PERSON>_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_to: type=inline

volumes:
  - name: docker_cache
    temp: {}
