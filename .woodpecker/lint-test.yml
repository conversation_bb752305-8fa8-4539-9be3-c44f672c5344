# Woodpecker CI - Lin<PERSON> and Test Pipeline

when:
  - branch: main
    event: [push,pull_request]

steps:
  - name: all-tests
    image: coinflow/dind-bun
    network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    #   - ${CI_WORKSPACE}:/workspace  # 将代码挂载到容器内
    environment:
      NODE_ENV: test
    commands:
      - ls -l $(echo $PATH | tr ':' '\n') | head -n 50
      - bun install:all
      - bun run lint:api
      - bun run lint:chatbot
      - bun run test:api
      - bun run test:integration -- --verbose --runInBand
    # Woodpecker 缓存语法
    cache:
      - path: node_modules
        key: bun-root-{{ hash "bun.lock" }}
      - path: api/node_modules
        key: bun-api-{{ hash "api/bun.lock" }}
      - path: chatbot/node_modules
        key: bun-chatbot-{{ hash "chatbot/bun.lock" }}
