# Woodpecker CI - Build Dependencies Base Images

when:
  - branch: main
    event: [push]
    path:
      include:
        - api/yarn.lock
        - chatbot/yarn.lock
  - event: manual

steps:
  - name: build-api-deps
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/api-deps
      dockerfile: api/Dockerfile.deps
      context: api/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_to: type=inline

  - name: build-chatbot-deps
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/chatbot-deps
      dockerfile: chatbot/Dockerfile.deps
      context: chatbot/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_to: type=inline
