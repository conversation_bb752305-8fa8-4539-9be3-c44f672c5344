import { setupTestContainers, forceCleanupTestEnvironment } from './testUtils';

// 端口检查函数
async function checkPort(port: number, serviceName: string): Promise<void> {
    const net = await import('net');
    const socket = new net.Socket();

    try {
        await new Promise((resolve, reject) => {
            socket.setTimeout(1000);
            socket.on('connect', () => {
                socket.destroy();
                reject(new Error(`端口 ${port} (${serviceName}) 已被占用`));
            });
            socket.on('error', () => {
                socket.destroy();
                resolve(true); // 端口未被占用，这是我们期望的
            });
            socket.on('timeout', () => {
                socket.destroy();
                resolve(true); // 超时也认为端口可用
            });
            socket.connect(port, 'localhost');
        });
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`端口检查警告 - ${serviceName} (${port}): ${errorMessage}`);
        // 不抛出错误，让测试继续
    }
}

// Jest全局设置入口
async function jestGlobalSetup() {
    console.log('🚀 启动全局测试环境...');
    console.log('📍 当前工作目录:', process.cwd());
    console.log('📍 NODE_ENV:', process.env.NODE_ENV);

    try {
        // 检查必需端口是否可用
        console.log('🔍 检查端口可用性...');
        await checkPort(3001, 'API');
        await checkPort(3000, 'Web');
        console.log('✅ 端口检查通过');

        // 启动测试环境
        console.log('🏗️ 启动测试容器...');
        const containers = await setupTestContainers();
        console.log('📦 容器启动结果:', Object.keys(containers));

        // 分类显示启动的服务
        const containerServices = Object.entries(containers)
            .filter(([, info]) => (info as any).container === 'docker')
            .map(([name]) => name);

        const bunServices = Object.entries(containers)
            .filter(([, info]) => (info as any).container === 'bun')
            .map(([name]) => name);

        if (containerServices.length > 0) {
            console.log(`✅ Docker容器服务已启动: ${containerServices.join(', ')}`);
        }

        if (bunServices.length > 0) {
            console.log(`✅ 开发环境服务已启动: ${bunServices.join(', ')}`);
        }

        console.log('🎉 全局测试环境已准备就绪');

        // 确保函数正常结束，不抛出异常
        return Promise.resolve();

    } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error(`❌ 全局测试环境启动失败:`);
        console.error(`错误类型: ${err.constructor.name}`);
        console.error(`错误信息: ${err.message}`);
        console.error(`错误堆栈: ${err.stack}`);

        // 尝试清理环境
        console.log('🧹 尝试清理测试环境...');
        try {
            await forceCleanupTestEnvironment();
            console.log('✅ 环境清理完成');
        } catch (cleanupError) {
            const cleanupErr = cleanupError instanceof Error ? cleanupError : new Error(String(cleanupError));
            console.error('❌ 环境清理失败:', cleanupErr.message);
        }

        // 重新抛出错误，让 Jest 知道 setup 失败
        throw err;
    }
}

// 导出Jest和其他环境的函数
export default jestGlobalSetup;
export { jestGlobalSetup as startApi };
export { jestGlobalSetup as startWeb };
