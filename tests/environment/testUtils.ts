import { TestEnvironmentOptions, TEST_PROFILES, DEFAULT_TEST_OPTIONS } from './testConfigManager';
import { saveTestEnvironment, EnvironmentConfig } from './testEnvironment';
import path from 'path';
import { execSync, spawn, ChildProcess } from 'child_process';
import fs from 'fs';
import net from 'net';

// PID文件元数据类型
type PidMeta = {
    pid: number;
    owner: 'test' | 'manual' | 'terminated';
    startedAt: number;
    service: string;
};

// PID文件路径 - 使用项目根目录
const PROJECT_ROOT = process.cwd();
const API_PID_FILE = path.resolve(PROJECT_ROOT, 'api/.nest-dev.pid');
const CHAT_PID_FILE = path.resolve(PROJECT_ROOT, 'chatbot/.nextjs-dev.pid');

let isEnvironmentRunning = false;
let bunDevProcess: ChildProcess | null = null;

/**
 * 读取PID文件
 */
function readPidFile(pidFile: string): PidMeta | null {
    try {
        if (fs.existsSync(pidFile)) {
            const content = fs.readFileSync(pidFile, 'utf8').trim();

            // 兼容旧格式（纯数字）
            if (/^\d+$/.test(content)) {
                const pid = parseInt(content);
                return { pid, owner: 'manual', startedAt: 0, service: path.basename(pidFile) };
            }

            // 新JSON格式
            return JSON.parse(content) as PidMeta;
        }
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`⚠️  读取PID文件失败 ${pidFile}: ${errorMessage}`);
    }
    return null;
}

/**
 * 检查进程是否存在
 */
function isProcessAlive(pid: number): boolean {
    try {
        return process.kill(pid, 0);
    } catch (error) {
        return false;
    }
}

/**
 * 检查是否有测试启动的进程正在运行
 */
function checkTestProcesses(): { api?: PidMeta; chatbot?: PidMeta } {
    const result: { api?: PidMeta; chatbot?: PidMeta } = {};

    const apiPid = readPidFile(API_PID_FILE);
    if (apiPid && apiPid.owner === 'test' && isProcessAlive(apiPid.pid)) {
        result.api = apiPid;
    }

    const chatPid = readPidFile(CHAT_PID_FILE);
    if (chatPid && chatPid.owner === 'test' && isProcessAlive(chatPid.pid)) {
        result.chatbot = chatPid;
    }

    return result;
}

/**
 * 检查端口是否监听（返回 true 表示端口已就绪）
 */
async function isPortReady(port: number, host: string = 'localhost'): Promise<boolean> {
    return new Promise(resolve => {
        const socket = new net.Socket();
        socket.setTimeout(1000);
        socket.once('connect', () => {
            socket.destroy();
            resolve(true);
        });
        const onError = () => {
            socket.destroy();
            resolve(false);
        };
        socket.once('error', onError);
        socket.once('timeout', onError);
        socket.connect(port, host);
    });
}

/**
 * 检查是否有外部开发进程正在运行
 */
function checkExternalProcesses(): { api?: PidMeta; chatbot?: PidMeta } {
    const result: { api?: PidMeta; chatbot?: PidMeta } = {};

    const apiPid = readPidFile(API_PID_FILE);
    if (apiPid && apiPid.owner === 'manual' && isProcessAlive(apiPid.pid)) {
        result.api = apiPid;
    }

    const chatPid = readPidFile(CHAT_PID_FILE);
    if (chatPid && chatPid.owner === 'manual' && isProcessAlive(chatPid.pid)) {
        result.chatbot = chatPid;
    }

    return result;
}

/**
 * 启动测试容器环境
 */
export async function setupTestContainers(options: TestEnvironmentOptions = DEFAULT_TEST_OPTIONS): Promise<Record<string, any>> {
    const profileName = options.profile || 'test';
    const profile = TEST_PROFILES[profileName];

    if (!profile) {
        throw new Error(`未知的测试环境配置: ${profileName}`);
    }

    console.log(`启动测试环境: ${profile.description}`);

    try {
        // 设置环境变量
        const env = {
            'POSTGRES_PORT': '5432',
            'SEARXNG_PORT': '18080',
            'OPENAI_MOCK_PORT': '5002',
            'API_PORT': '3001',
            'WEB_PORT': '3000'
        };

        // 仅启动必需的本地容器服务；searxng 已有外部部署，无需在本地等待
        const containerServices = ['postgres', 'openai-mock'];
        const appServices = ['api', 'web'];
        const allServices = [...containerServices, ...appServices];

        // 如果环境已经运行，检查容器状态并重用
        if (isEnvironmentRunning) {
            console.log('测试环境已存在，检查容器状态...');
            const runningContainers = await checkDockerComposeStatus();

            if (runningContainers.length === containerServices.length) {
                console.log('Docker Compose 容器都在运行，重用现有环境');
                // 仍需启动bun dev进程
                await startBunDev(env);
                await waitForServices(appServices, env);
                return await collectContainerInfo(allServices, env);
            } else {
                console.log('部分容器未运行，重新启动容器服务');
            }
        }

        // 停止可能存在的bun dev进程，但保持docker-compose运行
        await stopBunDev();

        // 检查并启动Docker Compose服务
        await ensureDockerComposeRunning(env);

        // 等待容器服务启动
        await waitForServices(containerServices, env);

        // 启动开发环境（API + Web）
        await startBunDev(env);

        // 等待应用服务启动
        await waitForServices(appServices, env);

        // 收集所有服务信息
        const containers = await collectContainerInfo(allServices, env);

        // 保存环境配置
        await saveEnvironmentConfiguration(containers);

        isEnvironmentRunning = true;
        console.log(`测试环境启动完成: ${Object.keys(containers).join(', ')}`);
        return containers;

    } catch (error) {
        console.error(`启动测试环境失败: ${error}`);
        await stopBunDev(); // 出错时只停止bun dev
        throw error;
    }
}

/**
 * 启动 bun dev 开发环境
 */
async function startBunDev(env: Record<string, string>): Promise<void> {
    // 检查是否已有测试进程在运行
    const testProcesses = checkTestProcesses();
    if (testProcesses.api || testProcesses.chatbot) {
        console.log('⚠️ 发现已有测试启动的开发环境进程在运行，跳过重复启动');
        if (testProcesses.api) console.log(`API进程: PID ${testProcesses.api.pid}`);
        if (testProcesses.chatbot) console.log(`Chatbot进程: PID ${testProcesses.chatbot.pid}`);
        return;
    }

    // 检查是否有外部开发进程在运行
    const externalProcesses = checkExternalProcesses();
    if (externalProcesses.api || externalProcesses.chatbot) {
        const apiPortReady = await isPortReady(parseInt(env.API_PORT));
        const webPortReady = await isPortReady(parseInt(env.WEB_PORT));

        if (apiPortReady && webPortReady) {
            console.log('⚠️ 发现外部开发者启动的进程在运行，将复用现有环境');
            if (externalProcesses.api) console.log(`外部API进程: PID ${externalProcesses.api.pid}`);
            if (externalProcesses.chatbot) console.log(`外部Chatbot进程: PID ${externalProcesses.chatbot.pid}`);
            return;
        } else {
            console.log('⚠️ 检测到外部开发进程端口未就绪，执行清理并重新启动开发环境');
            await forceCleanupProcesses();
            // 继续执行后续启动流程
        }
    }

    if (bunDevProcess) {
        console.log('⚠️ 开发环境进程已在运行，跳过重复启动');
        return;
    }

    console.log('🚀 启动开发环境 (API + Web)...');
    console.log(`API 端口: ${env.API_PORT}`);
    console.log(`Web 端口: ${env.WEB_PORT}`);

    try {
        // 若之前异常退出但进程句柄未清理，确保停止
        await stopBunDev();

        // 设置开发环境变量
        const devEnv = {
            ...process.env,
            // NODE_ENV: 'development',
            // TEST_RUNNER: 'true', // 🔑 标记为测试启动
            // // 数据库配置
            // DATABASE_URL: `postgresql://postgres:postgres@localhost:${env.POSTGRES_PORT}/ai_server`,
            // DB_HOST: 'localhost',
            // DB_PORT: env.POSTGRES_PORT,
            // DB_USERNAME: 'postgres',
            // DB_PASSWORD: 'postgres',
            // DB_DATABASE: 'ai_server',
            // // 外部服务配置
            // SEARXNG_API_URL: `http://localhost:${env.SEARXNG_PORT}`,
            // OPENAI_API_BASE_URL: `http://localhost:${env.OPENAI_MOCK_PORT}`,
            // OPENAI_API_KEY: 'mock-api-key',
            // OPENAI_MODEL_NAME: 'gpt-3.5-turbo',
            // // Web 环境变量
            // NEXT_PUBLIC_API_URL: `http://localhost:${env.API_PORT}`
        };

        // 启动开发环境：只启动API服务，避免chatbot的Turbopack问题
        console.log(`执行命令: bun dev:mock (TEST_RUNNER=true)`);

        bunDevProcess = spawn('bun', ['dev:mock'], {
            cwd: process.cwd(),
            stdio: 'inherit', // 直接继承父进程的输出，让日志正常显示
            env: devEnv,
            detached: true, // 关键：创建独立的进程组
        });

        // 关键：告诉父进程(Jest)的事件循环，无需等待这个子进程
        // 这可以防止Jest在测试结束后因为"Open Handles"而挂起
        bunDevProcess.unref();

        bunDevProcess.on('exit', (code, signal) => {
            // 修复：SIGTERM 和 SIGINT 信号应该被视为正常退出
            if (signal === 'SIGTERM' || signal === 'SIGINT') {
                console.log(`✅ bun dev 进程组正常关闭 (信号: ${signal})`);
                // 不输出错误信息，避免混淆
            } else if (signal) {
                console.log(`⚠️ bun dev 进程组因信号 ${signal} 退出`);
            } else if (code === 0) {
                console.log(`✅ bun dev 进程组正常退出`);
            } else if (code === 1 && process.env.TEST_RUNNER === 'true') {
                // 在测试环境中，concurrently 在收到 SIGTERM 后返回退出码1是正常的
                console.log(`ℹ️ bun dev 进程组在测试环境中正常终止 (退出码: ${code})`);
            } else {
                console.log(`⚠️ bun dev 进程组退出，代码: ${code ?? 'unknown'}`);
            }
            bunDevProcess = null;
        });

        bunDevProcess.on('error', (error) => {
            console.error(`❌ bun dev 进程启动失败: ${error.message}`);
            bunDevProcess = null;
        });

        // 等待进程启动
        await new Promise(resolve => setTimeout(resolve, 5000));
        console.log('✅ 开发环境已启动（owner: test）');

    } catch (error) {
        console.error(`启动开发环境失败: ${error}`);
        throw error;
    }
}

/**
 * 停止 bun dev 开发环境
 */
async function stopBunDev(): Promise<void> {
    console.log('🛑 开始停止测试启动的开发环境...');

    if (bunDevProcess && bunDevProcess.pid && isProcessAlive(bunDevProcess.pid)) {
        const pid = bunDevProcess.pid;
        console.log(`🎯 发现 bun dev 主进程 (PID: ${pid})`);

        try {
            // 向整个进程组发送SIGTERM
            console.log(`📡 向进程组 ${pid} 发送 SIGTERM 信号`);
            process.kill(-pid, 'SIGTERM');

            // 等待进程退出，减少超时时间到10秒
            await waitForProcessExit(pid, 10000);

        } catch (err) {
            const error = err instanceof Error ? err : new Error(String(err));

            // 检查是否是权限问题或进程不存在
            if (error.message.includes('EPERM')) {
                console.log('ℹ️ 向进程组发送 SIGTERM 信号失败: 权限不足，进程可能已由其他方式终止');
            } else if (error.message.includes('ESRCH') || error.message.includes('No such process')) {
                console.log('ℹ️ 进程已经退出，清理完成');
            } else if (error.message.includes('exited with code 1') || error.message.includes('SIGTERM')) {
                console.log('ℹ️ concurrently 进程以退出码 1 结束，这是正常的终止行为');
            } else {
                console.warn(`⚠️ 停止 bun dev 进程组 (PID: ${pid}) 时出现警告: ${error.message}`);
                // Fallback to pkill - 但要小心不要误杀其他进程
                await forceCleanupProcesses();
            }
        }

        // 无论如何都标记为成功，因为我们的目标是停止进程
        console.log('✅ 开发环境停止流程完成');
        bunDevProcess = null;

    } else {
        // 如果没有全局进程句柄，尝试从PID文件恢复
        const devProcesses = checkTestProcesses();
        const mainDevPid = devProcesses.api?.pid || devProcesses.chatbot?.pid;

        if (mainDevPid && isProcessAlive(mainDevPid)) {
            console.log(`🎯 从 PID 文件发现主进程 (PID: ${mainDevPid})`);
            try {
                console.log(`📡 向进程组 ${mainDevPid} 发送 SIGTERM 信号`);
                process.kill(-mainDevPid, 'SIGTERM');
                await waitForProcessExit(mainDevPid, 10000);
            } catch (err) {
                const error = err instanceof Error ? err : new Error(String(err));

                // 检查是否是权限问题或进程不存在
                if (error.message.includes('EPERM')) {
                    console.log('ℹ️ 向进程组发送 SIGTERM 信号失败: 权限不足，进程可能已由其他方式终止');
                } else if (error.message.includes('ESRCH') || error.message.includes('No such process')) {
                    console.log('ℹ️ 进程已经退出，清理完成');
                } else if (error.message.includes('exited with code 1') || error.message.includes('SIGTERM')) {
                    console.log('ℹ️ concurrently 进程以退出码 1 结束，这是正常的终止行为');
                } else {
                    console.warn(`⚠️ 停止 bun dev 进程组 (PID: ${mainDevPid}) 时出现警告: ${error.message}`);
                    await forceCleanupProcesses();
                }
            }
        } else {
            console.log('ℹ️ 未发现由测试启动的开发环境进程');
        }
    }

    // 可选：在 CI 环境中进行更彻底的清理。
    // 经验表明，一些粗暴的 pkill 会误杀当前 job 进程组，导致退出码 129 (SIGHUP)。
    // 如果确有需要，可在工作流中显式调用 `bun clean:zombie`。这里默认跳过。

    // 不再调用 cleanupDockerCompose()，保持容器运行
    isEnvironmentRunning = false; // 重置状态，下次测试时重新检查

    console.log('✅ 开发环境停止流程完成');
}

/**
 * 等待指定PID的进程退出
 */
async function waitForProcessExit(pid: number, timeout: number = 15000): Promise<void> {
    const startTime = Date.now();
    console.log(`⏳ 等待进程 ${pid} 退出... (超时: ${timeout / 1000}s)`);

    while (isProcessAlive(pid)) {
        if (Date.now() - startTime > timeout) {
            console.warn(`⏰ 等待进程 ${pid} 超时 (${timeout / 1000}s)，尝试强制终止`);
            try {
                process.kill(-pid, 'SIGKILL');
                // 给一点时间让 SIGKILL 生效
                await new Promise(resolve => setTimeout(resolve, 500));

                if (isProcessAlive(pid)) {
                    console.warn(`⚠️ 强制终止进程 ${pid} 后仍然存活，可能是僵尸进程`);
                } else {
                    console.log(`✅ 进程 ${pid} 被强制终止`);
                }
            } catch (e) {
                // 忽略错误，可能进程已经消失
                console.log(`ℹ️ 进程 ${pid} 可能已经退出`);
            }
            return;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    console.log(`✅ 进程 ${pid} 已成功退出`);
}

/**
 * 检查Docker Compose容器状态
 */
async function checkDockerComposeStatus(): Promise<string[]> {
    const composeFile = path.join(process.cwd(), 'docker-compose.yml');

    try {
        const output = execSync(`docker-compose -f ${composeFile} ps --services --filter status=running`, {
            encoding: 'utf8',
            stdio: 'pipe'
        });
        const runningServices = output.trim().split('\n').filter(service => service.length > 0);
        console.log(`当前运行的Docker Compose服务: ${runningServices.join(', ')}`);
        return runningServices;
    } catch (error) {
        console.warn(`检查Docker Compose状态失败: ${error}`);
        return [];
    }
}

/**
 * 确保Docker Compose服务运行
 */
async function ensureDockerComposeRunning(env: Record<string, string>): Promise<void> {
    const composeFile = path.join(process.cwd(), 'docker-compose.yml');
    console.log('🔍 检查 docker-compose 文件:', composeFile);

    // 检查 docker-compose.yml 文件是否存在
    if (!fs.existsSync(composeFile)) {
        throw new Error(`docker-compose.yml 文件不存在: ${composeFile}`);
    }

    const runningContainers = await checkDockerComposeStatus();
    // searxng 已外部托管，Docker Compose 不再管理
    const requiredServices = ['postgres', 'openai-mock'];

    const missingServices = requiredServices.filter(service => !runningContainers.includes(service));

    if (missingServices.length > 0) {
        console.log(`🚀 需要启动的服务: ${missingServices.join(', ')}`);
        console.log('📋 当前运行的容器:', runningContainers.join(', ') || '无');

        // 构建环境变量字符串
        const envString = Object.entries(env).map(([key, value]) => `${key}=${value}`).join(' ');

        // 启动所有默认服务（postgres, searxng, openai-mock）
        const cmd = `${envString} docker-compose -f ${composeFile} up -d`;

        console.log(`🔧 执行命令: ${cmd}`);

        try {
            // 使用 stdio: 'inherit' 来获取详细输出
            execSync(cmd, { stdio: 'inherit', cwd: process.cwd() });
            console.log('✅ Docker Compose服务已启动');

            // 等待服务就绪
            console.log('⏳ 等待服务就绪...');
            await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒

            // 再次检查状态
            const newRunningContainers = await checkDockerComposeStatus();
            console.log('📋 启动后运行的容器:', newRunningContainers.join(', ') || '无');

        } catch (error) {
            console.error('❌ Docker Compose 启动失败:');
            const err = error instanceof Error ? error : new Error(String(error));
            console.error('错误信息:', err.message);
            console.error('错误状态:', (err as any).status);
            console.error('命令输出:', (err as any).stdout?.toString());
            console.error('错误输出:', (err as any).stderr?.toString());

            // 尝试获取更多诊断信息
            try {
                console.log('🔍 Docker 诊断信息:');
                execSync('docker --version', { stdio: 'inherit' });
                execSync('docker-compose --version', { stdio: 'inherit' });
                execSync('docker ps -a', { stdio: 'inherit' });
            } catch (diagError) {
                const diagErr = diagError instanceof Error ? diagError : new Error(String(diagError));
                console.warn('⚠️ 无法获取 Docker 诊断信息:', diagErr.message);
            }

            throw err;
        }
    } else {
        console.log('✅ 所有Docker Compose服务都在运行');
    }
}

/**
 * 清理Docker Compose环境（手动清理时使用）
 */
export async function cleanupDockerCompose(): Promise<void> {
    console.log('🧹 清理Docker Compose测试环境...');

    const composeFile = path.join(process.cwd(), 'docker-compose.yml');

    try {
        // 使用 -v 参数同时清理卷
        execSync(`docker-compose -f ${composeFile} down -v`, { stdio: 'inherit' });
        console.log('Docker Compose服务和卷已清理完成');
    } catch (error) {
        console.warn(`Docker Compose清理失败: ${error}`);
    }

    isEnvironmentRunning = false;
    console.log('测试环境清理完成');
}

/**
 * 等待服务启动
 */
async function waitForServices(services: string[], env: Record<string, string>): Promise<void> {
    const maxAttempts = 30;
    const delay = 2000;

    for (const service of services) {
        console.log(`等待服务启动: ${service}`);

        let attempts = 0;
        let isServiceReady = false;

        while (attempts < maxAttempts && !isServiceReady) {
            try {
                const port = getServicePort(service, env);
                if (port) {
                    // 简单的端口检查，使用跨平台的方法
                    const net = await import('net');
                    const socket = new net.Socket();

                    try {
                        await new Promise((resolve, reject) => {
                            socket.setTimeout(2000);
                            socket.on('connect', () => {
                                socket.destroy();
                                resolve(true);
                            });
                            socket.on('error', reject);
                            socket.on('timeout', reject);
                            socket.connect(port, 'localhost');
                        });
                        console.log(`服务 ${service} 已就绪`);
                        isServiceReady = true;
                    } catch (portError) {
                        // 端口未就绪，继续等待
                        console.log(`服务 ${service} 端口 ${port} 尚未就绪，等待中... (${attempts + 1}/${maxAttempts})`);
                    }
                } else {
                    console.warn(`服务 ${service} 没有配置端口，跳过检查`);
                    isServiceReady = true;
                }
            } catch (error) {
                console.warn(`检查服务 ${service} 时出错: ${error}`);
            }

            if (!isServiceReady) {
                attempts++;
                if (attempts >= maxAttempts) {
                    throw new Error(`服务 ${service} 启动超时 (尝试了 ${maxAttempts} 次)`);
                }
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
}

/**
 * 获取服务端口
 */
function getServicePort(service: string, env: Record<string, string>): number | undefined {
    const portMap: Record<string, string> = {
        postgres: env.POSTGRES_PORT,
        searxng: env.SEARXNG_PORT,
        'openai-mock': env.OPENAI_MOCK_PORT,
        api: env.API_PORT,
        web: env.WEB_PORT
    };

    return portMap[service] ? parseInt(portMap[service]) : undefined;
}

/**
 * 收集容器信息
 */
async function collectContainerInfo(services: string[], env?: Record<string, string>): Promise<Record<string, any>> {
    const containers: Record<string, any> = {};

    const servicePortMap: Record<string, number> = {
        postgres: parseInt(env?.POSTGRES_PORT || '5432'),
        searxng: parseInt(env?.SEARXNG_PORT || '18080'),
        'openai-mock': parseInt(env?.OPENAI_MOCK_PORT || '5002'),
        api: parseInt(env?.API_PORT || '3002'),  // 改为3002
        web: parseInt(env?.WEB_PORT || '3000'),
    };

    for (const serviceName of services) {
        const port = servicePortMap[serviceName];
        if (!port) continue;

        try {
            const host = 'localhost';
            const url = `http://${host}:${port}`;

            containers[serviceName] = {
                url,
                host,
                port,
                container: ['api', 'web'].includes(serviceName) ? 'bun' : 'docker'
            };

            console.log(`${serviceName} 服务地址: ${url}`);
        } catch (error) {
            console.warn(`无法获取 ${serviceName} 服务信息: ${error}`);
        }
    }

    return containers;
}

/**
 * 保存环境配置
 */
async function saveEnvironmentConfiguration(containers: Record<string, any>): Promise<void> {
    const envConfig: Partial<EnvironmentConfig> = {};

    // PostgreSQL 配置
    if (containers.postgres) {
        const { host, port } = containers.postgres;
        envConfig.DATABASE_URL = `postgresql://postgres:postgres@${host}:${port}/ai_server`;
        envConfig.DB_HOST = host;
        envConfig.DB_PORT = String(port);
        envConfig.DB_USERNAME = 'postgres';
        envConfig.DB_PASSWORD = 'postgres';
        envConfig.DB_DATABASE = 'ai_server';
    }

    // SearXNG 配置
    if (containers.searxng) {
        envConfig.SEARXNG_API_URL = containers.searxng.url;
    }

    // OpenAI Mock 配置
    if (containers['openai-mock']) {
        envConfig.OPENAI_API_BASE_URL = containers['openai-mock'].url;
        envConfig.OPENAI_API_KEY = 'mock-api-key';
        envConfig.OPENAI_MODEL_NAME = 'gpt-3.5-turbo';
    }

    saveTestEnvironment(envConfig);
    console.log('测试环境配置已保存到 .env.test 文件');
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
    // 优先使用环境变量，如果没有则使用默认端口3001
    const port = process.env.API_PORT || '3001';
    const baseUrl = `http://localhost:${port}`;

    // 添加调试信息
    console.log('getApiBaseUrl调试信息:', {
        NODE_ENV: process.env.NODE_ENV,
        API_PORT: process.env.API_PORT,
        port,
        baseUrl
    });

    return baseUrl;
}

/**
 * 手动清理所有测试环境（包括Docker Compose容器）
 * 这个函数用于需要完全重置环境的情况
 */
export async function forceCleanupTestEnvironment(): Promise<void> {
    console.log('🧹 强制清理所有测试环境...');
    await stopBunDev();
    await cleanupDockerCompose();
    console.log('✅ 所有测试环境已清理');
}

/**
 * 强制清理所有已知的开发和测试进程
 */
async function forceCleanupProcesses(verbose: boolean = false): Promise<void> {
    if (verbose) {
        console.log('🧹 CI 环境检测到，执行彻底清理...');
    }

    const cleanupCommands = [
        'pkill -f "bun.*dev" || true',
        'pkill -f "nest.*start" || true',
        'pkill -f "next.*dev" || true',
        'pkill -f "concurrently" || true',
        'pkill -f "NODE_ENV=mock" || true'
    ];

    for (const cmd of cleanupCommands) {
        try {
            if (verbose) console.log(`✅ 执行清理命令: ${cmd}`);
            execSync(cmd, { stdio: 'ignore' });
        } catch (error) {
            if (verbose) console.warn(`⚠️  清理命令 "${cmd}" 执行失败: ${error}`);
        }
    }

    if (verbose) {
        console.log('🧽 CI 环境清理完成');
    }
}

// 兼容旧测试脚本的导出: 简化版清理，仅停止 bun dev
export async function teardownTestContainers(): Promise<void> {
    try {
        await stopBunDev();
    } catch (error) {
        // 捕获所有错误，避免影响测试退出码
        const err = error instanceof Error ? error : new Error(String(error));
        console.log(`ℹ️ 清理过程中的非关键错误: ${err.message}`);

        // 检查是否是 concurrently 的正常退出
        if (err.message.includes('exited with code 1') || err.message.includes('SIGTERM')) {
            console.log('ℹ️ 这是 concurrently 在收到终止信号时的正常行为');
        }
    } finally {
        isEnvironmentRunning = false;
        console.log('✅ 测试环境清理标记完成');
    }
}
