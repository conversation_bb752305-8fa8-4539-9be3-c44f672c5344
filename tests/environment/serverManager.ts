// tests/environment/serverManager.ts
import { setupTestContainers, teardownTestContainers } from './testUtils';
import net from 'net';

// 检查端口是否可用，如果是我们自己的服务则允许
async function checkPort(port: number, service: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const server = net.createServer();

        server.listen(port, (err?: any) => {
            if (err) {
                // 检查是否是我们期望的服务
                if (service === 'API' || service === 'Web') {
                    // 对于API和Web服务，端口被占用是可以的（可能是之前的测试环境）
                    console.log(`端口 ${port} (${service}) 已被占用，可能是之前的测试环境`);
                    resolve();
                } else {
                    reject(new Error(`端口 ${port} (${service}) 被非预期服务占用，请释放端口后重试`));
                }
            } else {
                server.close(() => resolve());
            }
        });

        server.on('error', () => {
            if (service === 'API' || service === 'Web') {
                console.log(`端口 ${port} (${service}) 已被占用，可能是之前的测试环境`);
                resolve();
            } else {
                reject(new Error(`端口 ${port} (${service}) 被非预期服务占用，请释放端口后重试`));
            }
        });
    });
}

// 启动API服务
export async function startApi() {
    console.log('🚀 启动API服务...');
    await checkPort(3001, 'API');
    console.log('✅ API服务检查完成');
}

// 启动Web服务
export async function startWeb() {
    console.log('🚀 启动Web服务...');
    await checkPort(3000, 'Web');
    console.log('✅ Web服务检查完成');
}

// Playwright全局设置
export async function globalSetup() {
    console.log('🚀 启动Playwright测试环境...');

    try {
        // 检查必需端口是否可用
        await startApi();
        await startWeb();

        console.log('✅ 端口检查通过');

        // 启动测试环境
        const containers = await setupTestContainers();

        // 分类显示启动的服务
        const containerServices = Object.entries(containers)
            .filter(([, info]) => (info as any).container === 'docker')
            .map(([name]) => name);

        const bunServices = Object.entries(containers)
            .filter(([, info]) => (info as any).container === 'bun')
            .map(([name]) => name);

        if (containerServices.length > 0) {
            console.log(`✅ Docker容器服务已启动: ${containerServices.join(', ')}`);
        }

        if (bunServices.length > 0) {
            console.log(`✅ 开发环境服务已启动: ${bunServices.join(', ')}`);
        }

        console.log('🎉 Playwright测试环境已准备就绪');
    } catch (error) {
        console.error(`❌ Playwright测试环境启动失败: ${error}`);
        // 确保失败时也清理环境
        await teardownTestContainers();
        throw error;
    }
}

// Playwright全局清理
export async function globalTeardown() {
    console.log('🧹 Playwright测试结束...');

    try {
        await teardownTestContainers();
        console.log('🎉 Playwright测试环境清理完成');
    } catch (error) {
        console.error(`❌ Playwright测试环境清理失败: ${error}`);
    }
}

// 清理所有服务
export async function stopAll() {
    await globalTeardown();
}
