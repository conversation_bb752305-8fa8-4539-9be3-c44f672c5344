import { teardownTestContainers } from './testUtils';

async function jestGlobalTeardown() {
    console.log('🧹 开始清理全局测试环境...');

    try {
        await teardownTestContainers();
        console.log('✅ 全局测试环境清理完成');

        // 强制退出时给进程一点时间完成清理
        await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
        console.log('⚠️ 全局测试环境清理过程中出现警告:');
        const err = error instanceof Error ? error : new Error(String(error));

        // 检查是否是正常的信号终止
        if (err.message.includes('SIGTERM') || err.message.includes('SIGINT')) {
            console.log('ℹ️ 进程被信号正常终止，这是预期的行为');
        } else {
            console.error(`错误类型: ${err.constructor.name}`);
            console.error(`错误信息: ${err.message}`);
            console.error(`错误堆栈: ${err.stack}`);
        }

        // 不重新抛出错误，避免影响测试结果
        // Jest 的 globalTeardown 应该尽量不失败
        console.log('✅ 测试清理已完成（忽略非关键错误）');
    }
}

export default jestGlobalTeardown;
