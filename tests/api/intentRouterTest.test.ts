import axios from 'axios';
import { logger } from '../environment/testUtils';
import { getApiBaseUrl, getAuthHeaders } from '../environment/testUtils';

describe('意图路由测试接口', () => {
    let apiBaseUrl: string;
    let authHeaders: any;

    beforeAll(async () => {
        apiBaseUrl = getApiBaseUrl();
        authHeaders = await getAuthHeaders();
        logger.info('🚀 开始意图路由测试接口测试');
    });

    afterAll(() => {
        logger.info('✅ 意图路由测试接口测试完成');
    });

    describe('POST /api/nest/intent-router/test - 基础功能测试', () => {
        it('应该正确路由市场数据查询', async () => {
            try {
                logger.info('测试市场数据查询路由...');

                const testData = {
                    query: 'BTC最新价格是多少？',
                    debugMode: true,
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(200);
                expect(response.data).toHaveProperty('selectedTool');
                expect(response.data).toHaveProperty('processingStages');
                expect(response.data).toHaveProperty('confidence');
                expect(response.data).toHaveProperty('processingTime');
                expect(response.data).toHaveProperty('availableTools');

                // 验证选择的工具应该是市场数据相关的
                expect(response.data.selectedTool).toBeTruthy();
                expect(typeof response.data.confidence).toBe('number');
                expect(response.data.confidence).toBeGreaterThanOrEqual(0);
                expect(response.data.confidence).toBeLessThanOrEqual(1);

                logger.info('✅ 市场数据查询路由测试通过:', {
                    selectedTool: response.data.selectedTool,
                    confidence: response.data.confidence,
                    processingTime: response.data.processingTime,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`市场数据查询路由测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`市场数据查询路由测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });

        it('应该正确路由搜索查询', async () => {
            try {
                logger.info('测试搜索查询路由...');

                const testData = {
                    query: '最近一周的AI新闻有哪些？',
                    debugMode: true,
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(200);
                expect(response.data.selectedTool).toBeTruthy();
                expect(response.data.processingStages).toHaveProperty('keyword');

                logger.info('✅ 搜索查询路由测试通过:', {
                    selectedTool: response.data.selectedTool,
                    confidence: response.data.confidence,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`搜索查询路由测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`搜索查询路由测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });

        it('应该正确路由通用对话查询', async () => {
            try {
                logger.info('测试通用对话查询路由...');

                const testData = {
                    query: '你好，请介绍一下你自己',
                    debugMode: true,
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(200);
                expect(response.data.selectedTool).toBeTruthy();

                logger.info('✅ 通用对话查询路由测试通过:', {
                    selectedTool: response.data.selectedTool,
                    confidence: response.data.confidence,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`通用对话查询路由测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`通用对话查询路由测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });
    });

    describe('POST /api/nest/intent-router/test - 参数验证测试', () => {
        it('应该验证必填参数', async () => {
            try {
                logger.info('测试必填参数验证...');

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    {}, // 缺少query参数
                    {
                        headers: authHeaders,
                        timeout: 10000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(400);
                expect(response.data.message).toBeDefined();

                logger.info('✅ 必填参数验证正常工作:', response.data.message);

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`参数验证测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`参数验证测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });

        it('应该验证工具范围参数', async () => {
            try {
                logger.info('测试工具范围参数验证...');

                const testData = {
                    query: 'BTC价格',
                    availableTools: ['non_existent_tool'], // 不存在的工具
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 10000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(400);
                expect(response.data.message).toContain('指定的工具不存在');

                logger.info('✅ 工具范围参数验证正常工作:', response.data.message);

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`工具范围验证测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`工具范围验证测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });
    });

    describe('POST /api/nest/intent-router/test - 高级功能测试', () => {
        it('应该支持指定可用工具范围', async () => {
            try {
                logger.info('测试指定工具范围功能...');

                const testData = {
                    query: 'BTC价格',
                    availableTools: ['chat_tool'], // 只允许使用聊天工具
                    debugMode: true,
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(200);
                expect(response.data.selectedTool).toBe('chat_tool');
                expect(response.data.availableTools).toEqual(['chat_tool']);

                logger.info('✅ 指定工具范围功能测试通过:', {
                    selectedTool: response.data.selectedTool,
                    availableTools: response.data.availableTools,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`指定工具范围测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`指定工具范围测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });

        it('应该支持调试模式', async () => {
            try {
                logger.info('测试调试模式功能...');

                const testData = {
                    query: 'BTC价格',
                    debugMode: true,
                };

                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );

                expect(response.status).toBe(200);
                expect(response.data).toHaveProperty('debugInfo');
                expect(response.data.debugInfo).toHaveProperty('originalQuery');
                expect(response.data.debugInfo).toHaveProperty('toolCount');
                expect(response.data.debugInfo).toHaveProperty('routeOptions');

                logger.info('✅ 调试模式功能测试通过:', {
                    debugInfo: response.data.debugInfo,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`调试模式测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`调试模式测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });
    });

    describe('POST /api/nest/intent-router/test - 性能测试', () => {
        it('应该在合理时间内完成路由', async () => {
            try {
                logger.info('测试路由性能...');

                const testData = {
                    query: 'BTC价格走势如何？',
                };

                const startTime = Date.now();
                const response = await axios.post(
                    `${apiBaseUrl}/api/nest/intent-router/test`,
                    testData,
                    {
                        headers: authHeaders,
                        timeout: 30000,
                        validateStatus: () => true,
                    }
                );
                const endTime = Date.now();

                expect(response.status).toBe(200);
                expect(response.data.processingTime).toBeLessThan(5000); // 应该在5秒内完成

                const totalTime = endTime - startTime;
                logger.info('✅ 路由性能测试通过:', {
                    processingTime: response.data.processingTime,
                    totalRequestTime: totalTime,
                });

            } catch (error) {
                if (axios.isAxiosError(error)) {
                    logger.error(`性能测试失败: ${error.message}`, error.response?.data);
                } else {
                    logger.error(`性能测试执行失败: ${String(error)}`);
                }
                throw error;
            }
        });
    });
});
