version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    container_name: postgres-ai
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ai_server
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - network

  # SearXNG 搜索引擎
  # searxng:
  #   image: searxng/searxng:latest
  #   container_name: searxng
  #   restart: unless-stopped
  #   environment:
  #     - SEARXNG_BASE_URL=http://localhost:${SEARXNG_PORT:-8080}
  #   ports:
  #     - "${SEARXNG_PORT:-8080}:8080"
  #   networks:
  #     - network

  # OpenAI Mock 服务（用于测试）
  openai-mock:
    image: coinflow/llmock:latest
    container_name: openai-mock
    restart: unless-stopped
    ports:
      - "${OPENAI_MOCK_PORT:-5002}:3000"
    networks:
      - network

networks:
  network:
    driver: bridge

volumes:
  postgres_dev_data:
