---
type: "agent_requested"
---

# API 开发规范

## 1. 项目通用开发规范

### 1.1 命名规范
- 文件名、变量、函数、常量等均采用 camelCase。
- 目录名采用小写字母，多个单词用连字符（-）连接。
- 不允许使用 index.ts 作为业务逻辑文件。
- 类型定义用 PascalCase，接口以 I 前缀，类型以 T 前缀。

### 1.2 目录结构
- 按功能模块组织目录，如 service、controller、utils、model、config。
- 每个模块单独目录，相关文件放在一起。
- 配置文件用 .config.ts 后缀，类型定义用 .types.ts，常量用 .constants.ts。

### 1.3 依赖与技术选型
- 数据库优先使用 pg 库，或 TypeORM/Sequelize 等主流ORM。
- 技术指标计算优先 technicalindicators 库，TD序列需自定义实现。
- 定时任务统一用 node-cron。
- API调用优先用 okx-api SDK，所有外部API请求必须经过 rateLimiter 工具。

### 1.4 配置管理
- 配置集中在 config/market.config.ts。
- 敏感信息（如API密钥、数据库连接）必须用环境变量管理。

### 1.5 错误处理与告警
- 关键流程必须有 try-catch，错误需日志记录。
- 支持异常告警（如邮件、IM等），可扩展。

### 1.6 代码风格
- 必须通过 ESLint、Prettier 检查。
- 重要函数、类、模块需有注释说明。

### 1.7 其他
- 所有新功能、变更需补充/更新文档。
- 相关测试必须覆盖主要业务逻辑。

## 2. 文件命名规范

### 2.1 文件命名规则
- 使用 `camelCase` 命名文件，例如：
  - `logger.ts`
  - `requestLogger.ts`
  - `searchService.ts`
  - `userController.ts`

### 2.2 避免使用 index.ts
- 不建议使用 `index.ts` 作为业务逻辑文件
- 原因：
  - 难以快速定位具体功能
  - 不利于代码导航
  - 可能导致文件过大，职责不清晰
- 替代方案：
  - 使用具体的功能名称作为文件名
  - 例如：
    - ❌ `src/service/index.ts`
    - ✅ `src/service/searchService.ts`
    - ✅ `src/service/userService.ts`

### 2.3 目录结构规范
- 按功能模块组织目录
- 每个模块使用独立的目录
- 目录名使用小写字母，多个单词用连字符（-）连接
- 例如：
  ```
  src/
  ├── config/              # 配置（*.config.ts）
  ├── auth/                # 认证 / 权限模块
  ├── dto/                 # 全局通用 DTO
  ├── infra/               # 基础设施层（第三方集成、队列、缓存等）
  ├── llm/                 # 大模型相关封装
  ├── entities/            # TypeORM 实体定义
  ├── middleware/
  │   ├── requestLogger.ts
  │   └── authMiddleware.ts
  ├── service/
  │   ├── searchService.ts
  │   └── userService.ts
  ├── controller/
  │   ├── searchController.ts
  │   └── userController.ts
  └── utils/
      ├── httpUtils.ts
      └── dateUtils.ts
  ```

### 2.4 特殊文件命名
- 配置文件：使用 `.config.ts` 后缀
  - 例如：`app.config.ts`
- 类型定义：使用 `.types.ts` 后缀
  - 例如：`user.types.ts`
- 常量文件：使用 `.constants.ts` 后缀
  - 例如：`api.constants.ts`

### 2.5 测试文件命名
- 测试文件使用 `.test.ts` 或 `.spec.ts` 后缀
- 测试文件应与被测试文件同名
- 例如：
  - `searchService.ts` 的测试文件为 `searchService.test.ts`
  - `userController.ts` 的测试文件为 `userController.test.ts`

## 3. 代码命名规范

### 3.1 变量命名
- 使用 `camelCase` 命名变量
- 布尔类型变量使用 `is`、`has`、`should` 等前缀
- 例如：
  ```typescript
  const userName = 'John';
  const isActive = true;
  const hasPermission = false;
  ```

### 3.2 函数命名
- 使用 `camelCase` 命名函数
- 动词开头，清晰表达功能
- 例如：
  ```typescript
  function getUserById(id: string) { }
  function calculateTotalPrice() { }
  function validateInput() { }
  ```

### 3.3 类命名
- 使用 `PascalCase` 命名类
- 名词开头，清晰表达实体
- 例如：
  ```typescript
  class UserService { }
  class DatabaseConnection { }
  class PaymentProcessor { }
  ```

### 3.4 接口命名
- 使用 `PascalCase` 命名接口
- 使用 `I` 前缀
- 例如：
  ```typescript
  interface IUserData { }
  interface IApiResponse { }
  interface IConfigOptions { }
  ```

### 3.5 类型命名
- 使用 `PascalCase` 命名类型
- 使用 `T` 前缀
- 例如：
  ```typescript
  type TUserRole = 'admin' | 'user';
  type TApiResponse<T> = { data: T; status: number };
  ```

## 4. 项目测试规范

### 4.1 测试文件命名
- 测试文件必须与被测文件同名，使用 .test.ts 或 .spec.ts 后缀。
- 例如：userService.ts 的测试文件为 userService.test.ts。
- 集成测试文件放在 tests/ 目录下，按功能模块组织。

### 4.2 测试目录结构
测试在主目录下的的tests
```
tests/
├── api/                    # API集成测试
│   ├── chat.test.ts       # 聊天接口测试
│   ├── search.test.ts     # 搜索接口测试
│   └── discover.test.ts   # 发现功能测试
├── environment/           # 测试环境配置
│   ├── testUtils.ts      # 测试工具函数
│   └── setup.ts          # 测试环境初始化
└── unit/                  # 单元测试
    ├── service/          # 服务层单元测试
    ├── controller/       # 控制器单元测试
    └── utils/            # 工具函数单元测试
```

### 4.3  集成测试
- 重要的API接口必须有集成测试
- 需要真实的Docker环境运行
- 测试文件统一放在 tests/api/ 目录下
- 必须包含错误处理和超时控制

### 4.4 测试工具和框架

#### 4.4.1 基础框架
- 使用 Jest 作为测试框架
- 使用 ts-jest 支持 TypeScript
- 使用 axios 进行HTTP请求测试（替代supertest）

#### 4.4.2 环境依赖
- 集成测试需要 docker-compose 环境
- 使用 testUtils.ts 统一管理测试环境配置
- 支持动态获取API地址和模型配置

#### 4.4.3 流式响应测试
- 对于流式API（如聊天接口），必须测试流响应
- 使用 responseType: 'stream' 处理流数据
- 必须包含超时控制和错误处理
- 验证流结束标记（如 [DONE]）

### 4.5 测试实现规范

#### 4.5.1 测试结构
```typescript
describe('功能模块名称', () => {
    let apiBaseUrl: string;
    let testConfig: any;

    beforeAll(async () => {
        // 环境初始化
        apiBaseUrl = getApiBaseUrl();
        // 获取测试配置（如可用模型）
    });

    it('应该处理正常场景', async () => {
        // 测试实现
    });

    it('应该处理异常场景', async () => {
        // 异常测试
    });
});
```

#### 4.5.2 错误处理
```typescript
try {
    // 测试逻辑
} catch (error) {
    if (error instanceof Error) {
        logger.error(`测试错误: ${error.message}`);
    } else {
        logger.error(`测试错误: ${String(error)}`);
    }
    throw error;
}
```

#### 4.5.3 超时控制
- 所有异步请求必须设置合理的超时时间
- 流式响应测试使用 Promise + timeout 模式
- 默认超时时间：60秒（可根据接口调整）

#### 4.5.4 日志记录
- 使用项目统一的 logger 进行日志记录
- 记录关键的测试步骤和结果
- 错误日志必须包含详细信息

### 4.6 测试场景覆盖

#### 4.6.1 API测试必须覆盖
- 正常请求和响应
- 参数验证（缺失、错误类型）
- 错误状态码处理
- 超时场景
- 流式响应（如适用）

#### 4.6.2 特殊场景
- 多轮对话测试
- 不同系统提示测试
- 边界值测试
- 并发请求测试

### 4.7 测试环境管理

#### 4.7.1 Docker环境
- 集成测试前必须启动 docker-compose 环境
- 使用 testUtils.ts 管理环境配置
- 支持多环境切换（开发、测试、CI）

#### 4.7.2 测试数据
- 使用真实但安全的测试数据
- 避免硬编码，使用配置文件管理
- 测试后必须清理临时数据

### 4.8 最佳实践

#### 4.8.1 代码质量
- 测试代码需易读、易维护，命名清晰
- 使用 async/await 处理异步操作
- 避免测试间的相互依赖

#### 4.8.2 性能考虑
- 合理设置测试超时时间
- 避免不必要的重复初始化
- 使用 beforeAll/afterAll 进行环境准备和清理

#### 4.8.3 CI/CD集成
- 测试应可自动化运行
- 支持在CI环境中运行集成测试
- 测试失败时提供清晰的错误信息

### 4.9 测试覆盖率
- 单元测试覆盖率需达标（建议80%以上）
- 集成测试覆盖核心API接口
- 在CI中强制检查覆盖率
- 发现bug需补充回归测试

## 5. 最佳实践

1. 保持命名的一致性
2. 使用有意义的名称，避免缩写
3. 避免使用数字作为文件名或变量名的开头
4. 文件名应该反映其内容的主要功能
5. 避免使用过于通用的名称（如 `util.ts`、`helper.ts`）
6. 相关文件应该放在同一个目录下
7. 避免过长的文件名，但也要确保名称具有描述性 
8. 删除重构之后，不再被引用的代码
9. 使用yarn来管理
10. 需要坚持单一职责原则
