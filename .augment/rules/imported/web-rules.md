---
type: "agent_requested"
description: "Example description"
---
# Web 开发规范

## 1. 通用开发规范

### 1.1 技术栈
- 使用 TypeScript
- 使用 React 及函数式组件
- 使用 Tailwind CSS 进行样式设计
- 支持国际化（i18n），采用 i18next
- 组件和页面需支持响应式设计与主题切换

### 1.2 目录与结构
- 组件文件放在 `src/components/`，每个组件单独文件，使用 PascalCase 命名
- 公共方法、类型、配置分别放在 `src/utils/`、`src/types/`、`src/lib/`
- 国际化资源放在 `public/messages/`，按语言代码命名
- 测试相关文件放在 `test/` 目录，分为 features、step-definitions、support、fixtures 等

### 1.3 命名规范
- 组件、类型、接口、文件均使用 PascalCase
- Props 接口命名为 组件名+Props
- 避免缩写和无意义命名，文件名需反映主要功能
- 相关文件应归类在同一目录下
- 避免 util、helper 等过于通用的文件名

### 1.4 组件开发要求
- 必须包含类型定义、注释、国际化支持、主题切换、响应式设计
- 组件导出采用默认导出，统一在 `components/index.ts` 汇总
- 内部辅助组件使用 render 前缀
- 不允许在代码中硬编码文本，所有文本均需通过 i18n

### 1.5 国际化规范
- 当前支持的语言：
  - 英语 (en) - 默认语言
  - 简体中文 (zh)
  - 繁体中文 (zh-TW)
  - 日语 (ja)
  - 韩语 (ko)
- 使用 `useTranslation('common')` 获取 t 函数
- 翻译键分模块分层级，保持所有语言文件结构一致
- 新增翻译键需同步所有语言
- 避免在代码中拼接翻译文本，优先使用插值

### 1.6 代码最佳实践
1. 保持命名一致性和可读性
2. 删除重构后未被引用的代码
3. 组件、方法、类型需有适当注释
4. 组件应有良好可测试性
5. 避免魔法数字和硬编码
6. 充分利用 TypeScript 类型系统
7. 代码风格遵循 Prettier/ESLint 规范

## 2. React组件命名规范

### 2.1 组件文件命名
- 使用 `PascalCase` 命名组件文件，例如：
  - `SearchInputBar.tsx`
  - `ChatAnswer.tsx`
  - `ResearchProgress.tsx`
  - `PageFooter.tsx`

### 2.2 组件定义
- 使用函数式组件，并使用 `React.FC<Props>` 类型定义
- 组件名称与文件名一致，使用 `PascalCase`
- 例如：
  ```typescript
  const SearchInputBar: React.FC<SearchInputBarProps> = ({ value, onChange, onSearch }) => {
    // 组件实现
  };
  ```

### 2.3 Props 接口命名
- 使用组件名称 + `Props` 后缀
- 使用 `interface` 而非 `type`
- 例如：
  ```typescript
  interface SearchInputBarProps {
    value?: string;
    onChange?: (value: string) => void;
    onSearch: (query: string) => void;
  }
  ```

### 2.4 内部辅助组件
- 如果组件内部有辅助组件，使用 `render` 前缀命名
- 例如：
  ```typescript
  const renderCodeBlock = ({ inline, className, children, ...props }: CodeProps) => {
    // 实现
  };
  ```

### 2.5 组件导出
- 每个组件文件默认导出一个主组件
- 在 `components/index.ts` 中统一导出所有组件
- 例如：
  ```typescript
  export { default as SearchInputBar } from './SearchInputBar';
  export { default as ChatAnswer } from './ChatAnswer';
  ```

## 3. 多语言支持规范

### 3.1 语言文件结构
- 所有翻译文件存放在 `public/messages/` 目录下
- 使用语言代码命名 JSON 文件，例如：
  - `en.json` - 英文
  - `zh.json` - 简体中文
  - `zh-TW.json` - 繁体中文
  - `ja.json` - 日语
  - `ko.json` - 韩语
  - `ptBR.json` - 巴西葡萄牙语

### 3.2 翻译键值结构
- 使用嵌套对象结构组织翻译内容
- 顶层按功能模块或类别分组
- 例如：
  ```json
  {
    "app": {
      "title": "去中心化的智能网关",
      "slogan": "您的Web3智能入口"
    },
    "action": {
      "copy": "复制",
      "copied": "已复制"
    }
  }
  ```

### 3.3 在组件中使用翻译
- 使用 `useTranslation` hook 获取翻译函数
- 使用命名空间 `common` 访问翻译
- 例如：
  ```typescript
  const { t } = useTranslation('common');

  // 使用翻译
  <button>{t('action.copy')}</button>
  ```

### 3.4 语言配置
- 在 `src/lib/i18nConfig.ts` 中定义支持的语言列表
- 在 `src/lib/i18n.ts` 中配置 i18next
- 支持自动语言检测和语言切换
- 例如：
  ```typescript
  // i18nConfig.ts
  export const locales = ['en', 'zh', 'zh-TW', 'ja', 'ko', 'ptBR'] as const;
  export const defaultLocale: Locale = 'en';

  // i18n.ts
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      // 配置项
    });
  ```

### 3.5 翻译文件添加规范
1. 添加新语言时：
   - 在 `public/messages/` 创建新的语言文件
   - 在 `src/lib/i18nConfig.ts` 的 `locales` 数组中添加语言代码
   - 在 `src/lib/i18n.ts` 中导入并注册新语言文件

2. 添加新翻译键时：
   - 在所有语言文件中同步添加相同的键
   - 保持键的层次结构一致
   - 优先在英文文件中添加，然后复制到其他语言文件中进行翻译

### 3.6 翻译最佳实践
- 使用有意义的翻译键，反映内容而非上下文
- 对于复杂的翻译，使用插值而非拼接
  ```typescript
  // 正确
  t('footer.copyright', { year: new Date().getFullYear() })

  // 错误
  t('footer.copyright').replace('{year}', new Date().getFullYear())
  ```
- 对于可复用的短语，使用共享翻译键
- 避免在代码中硬编码文本，始终使用翻译函数

## 4. 组件开发要求

### 4.1 技术要求
- 使用TypeScript
- 使用Tailwind CSS进行样式设计
- 支持国际化(i18n)
- 符合项目的React最佳实践

### 4.2 组件结构
请包含以下内容:
- 适当的导入语句
- 准确的TypeScript接口定义
- 清晰的组件实现
- 适当的注释
- 必要的国际化支持
- 响应式设计
- 主题切换支持

## 5. 测试规范

### 5.1 测试类型
- 端到端测试（E2E）：使用 Cypress，目录为 `test/features/`，以 `.cy.ts` 结尾
- BDD 行为驱动测试：使用 Cucumber 语法，`.feature` 文件与步骤定义 `.steps.ts` 文件配套
- 组件单元测试：推荐使用 Jest + React Testing Library，测试文件建议放在 `test/unit/`，以 `.test.tsx` 结尾

### 5.2 目录结构
- `test/features/`：E2E 测试与 BDD 特性文件
- `test/step-definitions/`：Cucumber 步骤定义
- `test/support/`：Cypress 配置与自定义命令
- `test/fixtures/`：测试数据
- `test/containers/`：测试容器管理

### 5.3 命名规范
- 测试文件与被测组件/功能同名，后缀为 `.cy.ts`、`.test.tsx`、`.feature`、`.steps.ts`
- describe/it/step/feature/scenario 名称需准确描述测试目标
- 使用 data-cy 属性作为选择器，避免依赖 class/id

### 5.4 E2E 测试要求
- 每个测试应独立、可重复
- 使用 before/beforeEach/after 钩子管理测试生命周期
- 推荐封装常用操作为自定义命令（见 test/support/commands.ts）
- 可用 cy.intercept 模拟 API 响应，或用 testcontainers 启动真实后端
- 测试结束需清理资源（如关闭容器）

### 5.5 BDD 测试要求
- .feature 文件用自然语言描述业务场景，结构清晰，专注单一功能
- 步骤定义应复用、简洁，参数化场景优先用 Scenario Outline
- 支持标签分类，便于选择性运行

### 5.6 组件测试要求
- 主要测试渲染、交互、状态变化、条件渲染、错误处理
- 使用 mock/stub 隔离外部依赖
- 测试用例应覆盖主要逻辑分支和边界条件

### 5.7 测试最佳实践
1. 所有新功能必须有对应测试
2. 测试用例应覆盖正向、逆向和边界场景
3. 测试数据集中管理，避免硬编码
4. 保持测试代码简洁、可维护、易读
5. 失败用例应能快速定位问题
6. 测试命名和注释需准确反映意图
7. 避免测试间状态污染，保证可重复性


## 6. 最佳实践总结

1. 保持命名的一致性
2. 使用有意义的名称，避免缩写
3. 避免使用数字作为文件名或变量名的开头
4. 文件名应该反映其内容的主要功能
5. 避免使用过于通用的名称（如 `util.ts`、`helper.ts`）
6. 相关文件应该放在同一个目录下
7. 避免过长的文件名，但也要确保名称具有描述性
8. 删除重构之后，不再被引用的代码
9. 使用yarn来管理
