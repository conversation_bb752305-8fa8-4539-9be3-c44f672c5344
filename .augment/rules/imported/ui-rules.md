---
type: "agent_requested"
description: "Example description"
---
# Chatbot UI 设计规范

基于当前项目的设计系统，为保持界面一致性和开发效率，制定以下UI设计规范。

## 1. 设计原则

### 1.1 一致性原则
- 保持视觉元素的统一性，包括颜色、字体、间距、圆角等
- 遵循既定的交互模式和用户体验流程
- 确保组件在不同状态下的表现一致

### 1.2 可访问性原则
- 支持键盘导航和屏幕阅读器
- 提供适当的焦点指示器
- 确保颜色对比度符合WCAG标准

### 1.3 响应式原则
- 适配不同屏幕尺寸和设备类型
- 使用灵活的布局系统
- 优化移动端用户体验

## 2. 颜色系统

### 2.1 主题色彩
基于CSS变量的主题系统，支持明暗模式切换：

```css
/* 明亮模式 */
--primary: 240 5.9% 10%;           /* 主色调 - 深灰黑 */
--primary-foreground: 0 0% 98%;    /* 主色调前景色 - 近白 */
--secondary: 240 4.8% 95.9%;       /* 次要色 - 浅灰 */
--background: 0 0% 100%;           /* 背景色 - 纯白 */
--foreground: 240 10% 3.9%;        /* 前景色 - 深灰 */
--muted: 240 4.8% 95.9%;          /* 静音色 - 浅灰 */
--accent: 240 4.8% 95.9%;         /* 强调色 - 浅灰 */
--border: 240 5.9% 90%;           /* 边框色 - 中灰 */

/* 暗黑模式 */
--primary: 0 0% 98%;              /* 主色调 - 近白 */
--primary-foreground: 240 5.9% 10%; /* 主色调前景色 - 深灰 */
--secondary: 240 3.7% 15.9%;      /* 次要色 - 深灰 */
--background: 240 10% 3.9%;       /* 背景色 - 深灰黑 */
--foreground: 0 0% 98%;           /* 前景色 - 近白 */
--muted: 240 3.7% 15.9%;         /* 静音色 - 深灰 */
--accent: 240 3.7% 15.9%;        /* 强调色 - 深灰 */
--border: 240 3.7% 15.9%;        /* 边框色 - 深灰 */
```

### 2.2 功能色彩
```css
--destructive: 0 84.2% 60.2%;     /* 危险色 - 红色 */
--destructive-foreground: 0 0% 98%; /* 危险色前景 */

/* 自定义功能色 */
blue-50, blue-100, blue-600, blue-700    /* 信息提示色 */
green-50, green-500, green-700           /* 成功状态色 */
red-50, red-100, red-600, red-700        /* 错误状态色 */
gray-50, gray-100, gray-200, gray-400    /* 中性色阶 */
zinc-700, zinc-800, zinc-900             /* 暗色系 */
```

### 2.3 颜色使用规范
- **主色调**: 用于主要按钮、链接、重要标题
- **次要色**: 用于次要按钮、背景区域
- **功能色**: 用于状态指示、反馈提示
- **中性色**: 用于文本、边框、分割线

## 3. 圆角系统

### 3.1 圆角规格
```css
rounded-sm: 2px      /* 小圆角 - 用于内嵌元素 */
rounded-md: 6px      /* 中圆角 - 用于按钮、输入框 */
rounded-lg: 8px      /* 大圆角 - 用于卡片、弹窗 */
rounded-xl: 12px     /* 超大圆角 - 用于容器、面板 */
rounded-2xl: 16px    /* 特大圆角 - 用于主要容器 */
rounded-full: 50%    /* 完全圆形 - 用于头像、图标按钮 */
```

### 3.2 使用原则
- **按钮**: 默认使用 `rounded-md`
- **输入框**: 使用 `rounded-2xl` 保持现代感
- **卡片容器**: 使用 `rounded-xl`
- **图标按钮**: 使用 `rounded-full`
- **小装饰元素**: 使用 `rounded-sm`

## 4. 间距系统

### 4.1 间距规格
```css
gap-1: 4px       /* 最小间距 - 紧密元素 */
gap-1.5: 6px     /* 小间距 - 相关元素 */
gap-2: 8px       /* 标准间距 - 常用间距 */
gap-3: 12px      /* 中等间距 - 组件间距 */
gap-4: 16px      /* 大间距 - 区块间距 */
gap-6: 24px      /* 超大间距 - 主要区域 */

p-1: 4px         /* 内边距 - 最小 */
p-1.5: 6px       /* 内边距 - 小 */
p-2: 8px         /* 内边距 - 标准 */
p-3: 12px        /* 内边距 - 中等 */
p-4: 16px        /* 内边距 - 大 */
p-6: 24px        /* 内边距 - 超大 */
```

### 4.2 应用场景
- **按钮内边距**: `p-2` (8px) 或 `p-[7px]` (自定义)
- **卡片内边距**: `p-4` (16px) 或 `p-6` (24px)
- **组件间距**: `gap-2` (8px) 标准间距
- **区块间距**: `gap-4` (16px) 或 `gap-6` (24px)

## 5. 按钮系统

### 5.1 按钮变体
基于 `class-variance-authority` 的按钮系统：

```typescript
// 按钮变体
variant: {
  default: 'bg-primary text-primary-foreground hover:bg-primary/90',
  destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  ghost: 'hover:bg-accent hover:text-accent-foreground',
  link: 'text-primary underline-offset-4 hover:underline',
}

// 按钮尺寸
size: {
  default: 'h-10 px-4 py-2',
  sm: 'h-9 rounded-md px-3',
  lg: 'h-11 rounded-md px-8',
  icon: 'h-10 w-10',
}
```

### 5.2 状态样式
```css
/* 悬停状态 */
hover:bg-zinc-200 dark:hover:bg-zinc-900

/* 禁用状态 */
disabled:pointer-events-none disabled:opacity-50

/* 焦点状态 */
focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring

/* 激活状态 */
active:bg-sidebar-accent active:text-sidebar-accent-foreground
```

### 5.3 特殊按钮样式
```css
/* 图标按钮 */
"rounded-full p-2.5 hover:bg-gray-100 transition-colors duration-200"

/* 工具栏按钮 */
"rounded-md p-[7px] h-fit dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200"

/* 选中状态按钮 */
"bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400"
```

## 6. 输入框系统

### 6.1 基础样式
```css
/* 主输入框 */
"min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-2xl !text-base bg-muted pb-10 dark:border-zinc-700"

/* 搜索输入框 */
"w-full px-4 py-3 text-base border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
```

### 6.2 状态样式
```css
/* 焦点状态 */
focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2

/* 禁用状态 */
disabled:cursor-not-allowed disabled:opacity-50

/* 错误状态 */
border-red-300 focus:ring-red-500 focus:border-red-500
```

## 7. 卡片和容器

### 7.1 卡片样式
```css
/* 基础卡片 */
"bg-white rounded-xl shadow-sm border border-gray-200 p-6"

/* 暗色模式适配 */
"dark:bg-zinc-800 dark:border-zinc-700"

/* 带阴影的容器 */
"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
```

### 7.2 布局容器
```css
/* 主容器 */
"mx-auto max-w-4xl px-4"

/* 弹性布局 */
"flex flex-col gap-4"
"flex flex-row items-center gap-2"

/* 绝对定位 */
"absolute bottom-0 p-2 w-fit flex flex-row justify-end gap-2"
```

## 8. 动画和过渡

### 8.1 过渡效果
```css
/* 标准过渡 */
transition-colors duration-200
transition-all ease-linear
transition-transform

/* 动画效果 */
animate-pulse
animate-spin
animate-in animate-out
```

### 8.2 Framer Motion 动画
```typescript
// 渐入渐出
initial={{ opacity: 0, y: 10 }}
animate={{ opacity: 1, y: 0 }}
exit={{ opacity: 0, y: 10 }}
transition={{ type: 'spring', stiffness: 300, damping: 20 }}

// 缩放动画
initial={{ opacity: 0, scale: 0.95 }}
animate={{ opacity: 1, scale: 1 }}
exit={{ opacity: 0, scale: 0.95 }}
```

## 9. 图标系统

### 9.1 图标规格
- **默认尺寸**: 16px (`size={16}`)
- **小图标**: 14px (`size={14}`)
- **中等图标**: 20px (`size={20}`)
- **大图标**: 24px (`size={24}`)

### 9.2 图标样式
```css
/* 图标容器 */
"[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0"

/* 图标按钮 */
"flex-shrink-0 bg-primary-50 p-2 rounded-lg"
```

## 10. 侧边栏系统

### 10.1 侧边栏样式
```css
/* 侧边栏容器 */
"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg"

/* 侧边栏菜单项 */
"flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm"

/* 侧边栏触发器 */
"h-7 w-7 rounded-full hover:bg-gray-100"
```

## 11. 响应式设计

### 11.1 断点系统
```css
sm: 640px    /* 小屏幕 */
md: 768px    /* 中等屏幕 */
lg: 1024px   /* 大屏幕 */
xl: 1280px   /* 超大屏幕 */
```

### 11.2 响应式类名
```css
/* 移动端优先 */
"w-full md:w-auto"
"hidden md:flex"
"px-4 lg:px-0"
"max-w-sm sm:max-w-md lg:max-w-2xl xl:max-w-4xl"
```

## 12. 状态指示

### 12.1 加载状态
```css
/* 加载动画 */
"animate-spin rounded-full h-14 w-14 border-t-3 border-b-3 border-primary-500"

/* 脉冲效果 */
"animate-pulse text-sm text-blue-500"
```

### 12.2 状态色彩
```css
/* 成功状态 */
"bg-green-50 text-green-700"

/* 警告状态 */
"bg-yellow-50 text-yellow-700"

/* 错误状态 */
"bg-red-50 text-red-700"

/* 信息状态 */
"bg-blue-50 text-blue-700"
```

## 13. 开发最佳实践

### 13.1 组件开发
- 使用 `React.FC<Props>` 类型定义
- 采用 `memo` 优化性能
- 实现防御性编程，处理边界情况
- 支持 `className` 属性扩展

### 13.2 样式管理
- 优先使用 Tailwind CSS 类名
- 使用 `cn()` 工具函数合并类名
- 通过 CSS 变量支持主题切换
- 避免内联样式，保持可维护性

### 13.3 可访问性
- 添加适当的 `aria-label` 和 `title` 属性
- 使用语义化的 HTML 标签
- 确保键盘导航支持
- 提供屏幕阅读器友好的文本

### 13.4 性能优化
- 使用 `memo` 避免不必要的重渲染
- 通过 `useCallback` 和 `useMemo` 优化函数和计算
- 实现虚拟滚动处理大列表
- 优化图片和资源加载

## 14. 组件库规范

### 14.1 组件结构
```typescript
// 组件接口定义
interface ComponentProps {
  // 必需属性
  required: string;
  // 可选属性
  optional?: boolean;
  // 事件处理
  onClick?: () => void;
  // 样式扩展
  className?: string;
}

// 组件实现
const Component: React.FC<ComponentProps> = memo(({ 
  required, 
  optional = false, 
  onClick, 
  className 
}) => {
  return (
    <div className={cn("base-styles", className)}>
      {/* 组件内容 */}
    </div>
  );
});
```

### 14.2 导出规范
- 每个组件文件默认导出一个主组件
- 使用 PascalCase 命名组件和文件

通过遵循这套UI设计规范，可以确保整个项目的视觉一致性和用户体验的连贯性，同时提高开发效率和代码可维护性。 