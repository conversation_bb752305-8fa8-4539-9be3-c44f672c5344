---
type: "always_apply"
---

保持单一职责原则
代码是给人看的，不是只在机器人运行就可以的，方法不要太长，简单容易阅读
考虑代码的可测试性
在实现业务需求的情况下，尽可能保持方案简洁
保存代码干净，不使用的代码已删除为主
后端规范业务逻辑和请求方法，前端需要按照后端的规范实现，不要使用打补丁的方式解决问题，我们需要知道核心逻辑，然后再进行思考，再进行解决。
修改完代码需要运行下lint和build做代码检查
除非必要，不然不要定义interface
按照顺序思考的逻辑进行
可替换,可测试，阅读性好
环境一直在常驻，已经运行了yarn run，直接测试即可


# 测试策略与规范

## 测试分层原则
遵循测试金字塔：**单元测试 > 集成测试 > E2E测试**

## 目录结构
- `api/tests/` : **L1 服务测试** (`*.test.ts`, Jest) - 单个服务逻辑，必须Mock外部依赖
- `tests/api/` : **L2 集成测试** (`*.test.ts`, Jest) - 多服务协同，真实HTTP请求
- `tests/e2e/` : **L3 端到端测试** (`*.spec.ts`, Playwright) - 完整用户流程
- `tests/environment/` : 测试环境管理（`serverManager.ts`, `testSetup.ts`, `teardown.ts`）
- `tests/config/` : 测试专用配置（`.env.test`等）

## 测试选择规则
**修改什么，测试什么**：
- 修改单个服务 → 运行对应的 `api/tests/xxx.test.ts`
- 修改API接口 → 运行对应的 `tests/api/xxx.test.ts`
- 修改前端UI → 运行对应的 `tests/e2e/xxx.spec.ts`
- 修改工具/LLM → 运行 `api/tests/` 相关服务测试
- 修改数据库实体 → 运行相关的集成测试

## 关键文件
- **playwright.config.ts** : 设置 `testDir=tests/e2e`，用 `serverManager` 做全局 setup/teardown
- **test-runner.ts** : 智能入口，自动判断用 Jest 还是 Playwright
- **tests/environment/serverManager.ts** : 统一进程管理，确保测试环境一致性

## 常用脚本
- `yarn test`      : 依序运行 API + E2E
- `yarn test:api`  : 仅 Jest (L1+L2)
- `yarn test:e2e`  : 仅 Playwright (L3)
- `yarn e2e:debug` : 调试模式 (`PWDEBUG=1`)
- `cd api && yarn test tests/xxx.test.ts` : 运行单个服务测试

## 测试编写规范
- **L1 服务测试**: 使用 `TestingModule`，Mock所有外部依赖（LlmService、数据库等）
- **L2 集成测试**: 使用真实服务器，测试完整API流程，可使用真实数据库
- **L3 E2E测试**: 测试完整用户交互流程，包括前后端协同

## Playwright 默认配置
- `fullyParallel: true`
- 测试超时 **10s**，`expect` 超时 **5s**
- 自动启停服务，确保环境隔离

## 质量门禁
- PR 必须通过：`lint` → `build` → `test`
- 新功能需同时补充对应层级的测试
- 重构代码必须保证现有测试100%通过
- 每次修改后优先运行相关测试，而非全量测试


## 质量门禁
- PR 必须通过：`lint` → `build` → `test`。
- 新功能需同时补充 **API** 与 **E2E** 测试。

