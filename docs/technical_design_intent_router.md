# 技术方案设计 (TSD): 智能意图路由 AI Agent

> **版本**: 3.0
> **日期**: 2025-01-16
> **关联 PRD**: `PRODUCT_SPEC_INTENT_ROUTER.md`

---

## 1. 概述与目标

本技术方案旨在将 PRD 中的"智能意图路由 AI 助手"落地为具体、可执行的软件架构。
方案将基于现有 NestJS 应用进行扩展，引入 **Agent Orchestration** 核心，以支持动态意图识别、多步推理（ReAct）和工具调用（Function Calling）。

**核心技术目标:**
- **构建一个统一的、可扩展的 Agent Orchestration**，作为所有用户查询的入口。
- **模块化注册外部工具与数据源**（MCP、搜索引擎、文档分析等）。
- **设计一套流式 API**，能向前端实时传递思考过程、工具调用状态和最终的富媒体结果。
- **实现文档处理与 RAG (Retrieval-Augmented Generation) 管道**。
- **优化 Prompt 策略**：分层设计，避免单一复杂 prompt，提升响应速度和准确性。

---

## 2. 整体架构设计

### 2.1 系统架构图

```mermaid
graph TD
    subgraph Frontend
        UI(统一交互界面)
    end

    subgraph Backend NestJS
        A[POST api/nest/chat] --> B[OrchestratorController];
        B --> C[AgentExecutionFacade];
        C -- 1. 初始查询 --> D{IntentRouterFacade};
        D -- 2. 意图分类 --> C;
        C -- 3. 执行 ReAct 循环 --> E{ReActLoopService};
        E -- 4. 决定调用工具 --> F[ToolRegistry];
        F -- 5. 查找并执行工具 --> G[Tools];
        G -- 6. 返回工具结果 --> C;
        C -- 7. 将结果反馈给 LLM --> E;
        C -- 8. 流式返回状态/结果 --> B;
    end

    subgraph Tools 工具生态
        T1[ChatTool<br/>通用对话]
        T2[WebSearchTool<br/>网络搜索]
        T3[MarketDataTool<br/>行情查询]
        T4[ProjectDataTool<br/>项目分析]
        T5[DocumentAnalysisTool<br/>文档分析]
        T6[FetchUrlTool<br/>网页抓取]
        T7[SummarizeTextTool<br/>文本摘要]
    end

    subgraph ExternalServices
        S1[LLM APIs: OpenAI, etc.]
        S2[MCP APIs]
        S3[VectorDB: Qdrant]
        S4[SearXNG]
        S5[File Storage: Server Storage]
    end

    G --> T1 & T2 & T3 & T4 & T5 & T6 & T7;
    T1 & E --> S1;
    T3 & T4 --> S2;
    T5 --> S3;
    T2 --> S4;
    T6 --> S4;
    T7 --> S1;
    UI -- 上传文件 --> S5;
    T5 -- 读取文件 --> S5;
    B -- SSE Stream --> UI;
```

### 2.2 详细请求时序图

```mermaid
sequenceDiagram
    participant F as 前端
    participant C as 控制器
    participant A as AgentExecutionFacade
    participant I as IntentRouterFacade
    participant R as ReActLoopService
    participant T as ToolRegistry
    participant Tool as 具体工具

    F -> C: 发送聊天请求
    C -> A: 执行聊天
    A -> F: 打开SSE流
    A -> I: 路由查询
    I -> A: 返回推荐工具
    A -> F: 发送开始事件

    loop 推理循环
        A -> R: 执行ReAct循环
        R -> R: 构建Prompt
        R -> L: 生成思考和行动
        L -> R: 返回结果
        R -> F: 发送思考事件

        alt 调用工具
            R -> T: 获取工具
            T -> R: 返回工具实例
            R -> F: 发送工具开始事件
            R -> Tool: 执行工具
            Tool -> R: 返回执行结果
            R -> F: 发送工具结束事件
        else 最终回答
            R -> F: 发送回答事件
            break
        end
    end

    A -> F: 发送结束事件
    A -> C: 返回流
    C -> F: 结束SSE流
```

---

## 3. 核心组件设计

### 3.1 AgentExecutionFacade（核心执行器）

**核心职责**：
- 作为统一的聊天执行入口，支持所有意图类型
- 协调意图路由、ReAct循环、上下文管理和缓存
- 管理流式响应和性能监控

**主要功能**：
1. **统一执行入口**：通过`execute()`方法接收用户消息，返回流式响应
2. **缓存机制**：查询缓存以提高响应速度
3. **意图路由**：调用IntentRouterFacade确定最合适的工具
4. **ReAct循环执行**：调用ReActLoopService执行思考-行动-观察循环
5. **错误处理**：提供优雅降级和错误恢复机制
6. **性能监控**：跟踪执行时间和资源使用

**执行流程**：
1. 接收用户查询和上下文信息
2. 检查缓存是否有匹配结果
3. 通过意图路由确定合适的工具
4. 执行ReAct推理循环
5. 更新上下文和缓存
6. 返回结果和性能指标

### 3.2 IntentRouterFacade（意图路由门面）

**核心职责**：
- 分析用户查询，识别用户意图和角色需求
- 整合角色识别和三种路由策略：关键词过滤、向量缩减、LLM投票
- 根据识别的角色选择合适的工具组合

**四阶段智能路由策略**：

1. **角色识别阶段** (RoleIdentificationStrategy)
   - 基于关键词和语义分析识别用户角色需求
   - 支持4种角色：通用对话、研究员、行情分析师、项目分析师
   - 为每个角色预定义工具组合和工作流程
   - 返回识别的角色和对应的工具候选列表

2. **关键词过滤阶段** (KeywordFilterStrategy)
   - 不使用大模型，速度最快
   - 基于预定义关键词进行文本匹配
   - 使用模糊匹配和最长公共子串等算法
   - 在角色工具范围内进行进一步过滤

3. **向量缩减阶段** (VectorReducerStrategy)
   - 使用Embedding模型计算语义相似度
   - 对查询和工具描述进行向量化
   - 使用余弦相似度等计算相关性
   - 进一步缩小候选工具范围

4. **LLM投票阶段** (LlmVotingStrategy)
   - 使用LLM进行最终工具选择
   - 结合角色上下文进行智能决策
   - 可配置超时时间（建议30秒以上）
   - 支持多模型投票和置信度评分
   - 返回最终推荐的工具名称

**路由结果**：
- **selectedRole**: 识别的用户角色
- **selectedTool**: 推荐的工具名称
- **processingStages**: 各阶段的处理结果
- **confidence**: 推荐工具的置信度
- **processingTime**: 路由处理时间
- **cacheHit**: 是否命中缓存
- **rolePrompt**: 角色特定的提示词

### 3.3 ToolRegistryService（工具注册表）

**核心职责**：
- 管理所有可用工具的注册、发现和实例化
- 提供工具查询和向量化功能

**主要功能**：
1. **register(tool)**: 注册工具实例并生成向量
2. **getTool(name)**: 获取指定工具
3. **listTools()**: 列出所有可用工具
4. **getToolVector(name)**: 获取工具的向量表示

**工具注册机制**：
- 通过NestJS的依赖注入自动注册
- 在模块初始化时完成工具注册
- 自动为工具生成向量表示，用于向量检索

### 3.4 ReActLoopService（ReAct循环服务）

**核心职责**：
- 执行ReAct推理循环（Reason-Action-Observation）
- 管理LLM调用和工具执行
- 解析LLM响应并执行相应操作

**主要功能**：
1. **execute()**: 执行完整的ReAct循环
2. **invokeLlm()**: 调用LLM生成思考和行动
3. **parseResponse()**: 解析LLM响应，提取思考和行动
4. **formatAction()**: 格式化行动为字符串

**ReAct循环流程**：
1. 构建包含上下文、历史和工具定义的Prompt
2. 调用LLM获取思考和行动
3. 解析LLM响应，提取思考和行动
4. 如有最终答案，返回结果
5. 如有行动，执行相应工具
6. 将工具执行结果添加到思考历史
7. 重复循环直到得到最终答案或达到最大轮次

### 3.5 PromptBuilder（提示词构建器）

**核心职责**：
- 根据角色、上下文、工具和历史构建动态Prompt
- 实现分层Prompt策略，支持角色特定的专业提示词
- 优化LLM响应质量和角色一致性

**主要功能**：
1. **build()**: 构建完整的ReAct Prompt，支持角色参数
2. **buildSystemPrompt()**: 构建系统提示词，根据角色选择专业模板
3. **buildRolePrompt()**: 构建角色特定的专业提示词
4. **formatTools()**: 格式化工具列表
5. **formatContext()**: 格式化上下文提示
6. **buildFallbackPrompt()**: 构建降级提示词

**角色提示词管理**：
- 为每个角色维护专门的提示词模板
- 支持角色切换时的提示词动态更新
- 确保角色特定的工作流程和输出格式
- 集成搜索结合策略到角色提示词中

**优化的系统提示词**：
- 区分简单和复杂问题的处理方式
- 明确指导LLM何时直接回答，何时使用工具
- 提供清晰的输出格式指导
- 包含工具描述和参数格式
- 根据角色调整专业性和输出风格

### 3.6 ToolExecutionEngine（工具执行引擎）

**核心职责**：
- 执行工具调用，支持单工具和批量执行
- 管理工具执行的缓存、超时和错误处理
- 支持依赖分析和并发执行

**主要功能**：
1. **execute()**: 统一执行入口
2. **executeSingleTask()**: 执行单个任务
3. **executeBatch()**: 执行批量任务
4. **executeToolWithCache()**: 带缓存的工具执行
5. **resolveTaskArguments()**: 解析任务参数依赖

**执行流程**：
1. 接收工具任务列表
2. 分析任务依赖关系
3. 按依赖层级执行任务
4. 处理缓存和错误情况
5. 返回执行结果

---

## 4. 工具生态系统

### 4.1 核心工具概述

#### ChatTool（通用对话）
- **功能**：处理通用对话、创意写作、知识问答
- **实现**：调用LLM进行直接对话，无需额外工具
- **关键词**：聊天、对话、问答、创意、写作

#### WebSearchTool（网络搜索）
- **功能**：搜索最新的网络信息，获取实时资讯
- **实现**：调用SearXNG进行网络搜索
- **关键词**：搜索、查询、最新、新闻、资讯

#### MarketDataTool（行情数据）
- **功能**：获取加密货币、股票等金融市场的实时行情数据
- **实现**：调用MCP行情API获取数据
- **关键词**：价格、行情、涨跌、BTC、ETH、股票

#### ProjectDataTool（项目分析）
- **功能**：分析区块链项目数据和指标
- **实现**：结合项目数据库和实时搜索
- **关键词**：项目、代币、分析、评估

#### DocumentAnalysisTool（文档分析）
- **功能**：分析上传的文档内容，支持PDF、Word等格式
- **实现**：执行RAG流程，结合向量检索和LLM生成
- **关键词**：文档、PDF、合同、报告、分析

#### FetchUrlTool（网页抓取）
- **功能**：抓取指定URL的网页内容
- **实现**：调用网页抓取服务，返回结构化内容
- **关键词**：网页、URL、抓取、内容

#### SummarizeTextTool（文本摘要）
- **功能**：对长文本进行摘要和关键信息提取
- **实现**：调用LLM进行文本摘要生成
- **关键词**：摘要、总结、提炼、精简

### 4.2 智能角色系统

根据产品需求，系统需要支持4个核心角色，每个角色都有专门的提示词和工作流程：

#### 4.2.1 角色定义

**1. 通用对话 (GeneralChat)**
- **功能**：处理日常对话、知识问答、创意写作等通用场景
- **触发条件**：问候语、常识问题、创意请求、一般性咨询
- **工具组合**：ChatTool
- **关键词**：你好、帮我写、解释、什么是、如何、为什么

**2. 研究员 (Researcher)**
- **功能**：深度研究和信息整合，提供全面的主题分析
- **触发条件**：需要深度调研、多角度分析、综合信息整理
- **工具组合**：WebSearchTool + FetchUrlTool + SummarizeTextTool + ChatTool
- **关键词**：研究、调研、分析、报告、综合、整理、深入了解

**3. 行情分析师 (MarketAnalyst)**
- **功能**：提供专业的市场分析和投资洞察
- **触发条件**：涉及价格、行情、市场趋势、投资建议
- **工具组合**：MarketDataTool + WebSearchTool + ChatTool
- **关键词**：价格、行情、涨跌、市值、交易量、K线、趋势、投资

**4. 项目分析师 (ProjectAnalyst)**
- **功能**：提供深度的项目分析和技术评估
- **触发条件**：涉及具体项目、协议、技术分析、项目评估
- **工具组合**：ProjectDataTool + WebSearchTool + DocumentAnalysisTool + ChatTool
- **关键词**：项目、协议、代币、白皮书、团队、技术、机制、风险

#### 4.2.2 角色工作流程

**研究员工作流程**：
1. 理解研究主题和范围
2. 搜索相关信息和最新动态
3. 抓取重要网页内容进行深度分析
4. 整合多源信息，形成综合报告
5. 提供结构化的研究结论和建议

**行情分析师工作流程**：
1. 获取实时市场数据和价格信息
2. 搜索最新市场动态和新闻
3. 查看专家观点和市场情绪
4. 分析技术指标和价格趋势
5. 综合分析给出专业投资建议

**项目分析师工作流程**：
1. 获取项目基础数据和代币信息
2. 搜索项目相关新闻和社区讨论
3. 分析项目文档、白皮书和技术资料
4. 评估团队背景和项目进展
5. 综合评估项目价值、风险和前景

---

## 5. API 设计

### 5.1 统一聊天接口

**端点**: `POST /api/nest/chat`

**请求格式**:
- **messages**: 对话历史
- **chatId**: 会话ID（可选）
- **options**: 配置选项（最大推理轮次、偏好工具等）

**响应格式** (SSE Stream):
- **agent_start**: 代理开始执行，包含意图路由信息
- **thought**: LLM的思考过程
- **tool_start**: 开始调用工具，包含工具名称和输入
- **tool_end**: 工具调用结束，包含结果
- **response**: 最终回答内容
- **agent_end**: 代理执行结束，包含性能统计
- **error**: 错误信息

### 5.2 意图路由测试接口

**端点**: `POST /api/nest/intent-router/test`

**请求格式**:
- **query**: 用户查询
- **availableTools**: 可用工具范围（可选）

**响应格式**:
- **selectedTool**: 推荐的工具名称
- **processingStages**: 各阶段处理结果
- **confidence**: 置信度（0-1）
- **processingTime**: 处理时间

---

## 6. RAG 文档分析系统

### 6.1 文档处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant Up as 上传控制器
    participant S as 存储
    participant W as 处理Worker
    participant V as 向量库
    participant A as Agent

    U -> Up: 上传文档
    Up -> S: 存储文件
    Up -> W: 触发处理
    W -> W: 解析内容
    W -> W: 文本切块
    W -> V: 存储向量
    U -> A: 询问问题
    A -> V: 检索相关内容
    V -> A: 返回文本块
    A -> A: 生成答案
    A -> U: 返回结果
```

### 6.2 文档数据模型

**核心实体**：
- **documents**: 文档元数据（用户ID、文件名、存储路径等）
- **document_chunks**: 文档切块（内容、向量ID、索引等）

**存储策略**：
- 元数据存储在关系数据库（PostgreSQL）
- 向量数据存储在向量数据库（Qdrant）
- 通过ID关联两种数据库的记录

---

## 7. Prompt 策略优化

### 7.1 分层 Prompt 设计

**核心理念**：
- 避免单一复杂prompt
- 根据不同场景使用专门的prompt
- 保持基础prompt简洁清晰

**分层结构**：
1. **基础系统prompt**：简洁明了，区分简单和复杂问题
2. **专业领域prompt**：针对特定领域（如行情分析、项目分析）
3. **工具特定prompt**：每个工具可以有自己的专门prompt

### 7.2 角色专业提示词设计

#### 7.2.1 通用对话角色提示词

```
你是一个友好、博学的AI助手，擅长日常对话和知识问答。

核心特点：
- 语言自然流畅，富有亲和力
- 知识面广泛，能够解答各类常识问题
- 善于创意写作和头脑风暴
- 提供实用的生活建议和指导

回答风格：
- 直接、简洁、易懂
- 适当使用比喻和例子
- 保持积极正面的态度
- 鼓励用户进一步思考和探索
```

#### 7.2.2 研究员角色提示词

```
你是一位专业的研究员，擅长深度调研和信息整合。

核心能力：
- 系统性思维，能够从多个角度分析问题
- 信息收集和筛选能力强
- 善于发现信息之间的关联和趋势
- 能够提供客观、全面的研究报告

工作方法：
1. 明确研究目标和范围
2. 广泛收集相关信息和数据
3. 深入分析关键内容和观点
4. 整合多源信息，形成结构化报告
5. 提供基于证据的结论和建议

重要原则：
- 始终从互联网搜索最新、最权威的信息
- 引用可靠来源，标注信息出处
- 保持客观中立，避免主观偏见
- 提供多角度分析，展示不同观点
```

#### 7.2.3 行情分析师角色提示词

```
你是一位专业的行情分析师，具备深厚的金融市场知识和分析能力。

专业领域：
- 加密货币市场分析
- 技术分析和基本面分析
- 市场趋势预测和风险评估
- 投资策略和资产配置建议

分析框架：
1. 获取最新的市场数据和价格信息
2. 搜索相关新闻、事件和市场动态
3. 分析技术指标和价格走势
4. 评估市场情绪和资金流向
5. 结合基本面因素给出综合判断

专业要求：
- 必须使用最新的市场数据
- 结合多个信息源进行分析
- 明确标注数据来源和时间
- 提供风险提示和免责声明
- 避免给出绝对的投资建议

输出格式：
- 当前价格和关键指标
- 技术分析和趋势判断
- 基本面分析和影响因素
- 风险评估和注意事项
```

#### 7.2.4 项目分析师角色提示词

```
你是一位专业的项目分析师，专注于区块链和加密货币项目的深度分析。

核心能力：
- 项目技术架构分析
- 代币经济学评估
- 团队背景和项目进展跟踪
- 竞争格局和市场定位分析
- 风险识别和投资价值评估

分析维度：
1. 项目基本信息和发展历程
2. 技术特点和创新亮点
3. 代币机制和经济模型
4. 团队背景和合作伙伴
5. 市场表现和社区活跃度
6. 竞争优势和潜在风险

工作流程：
1. 收集项目官方信息和白皮书
2. 搜索最新的项目动态和新闻
3. 分析项目技术文档和代码
4. 评估团队实力和执行能力
5. 对比竞争项目和市场地位
6. 综合评估投资价值和风险

重要原则：
- 基于事实和数据进行分析
- 保持客观中立的立场
- 及时更新项目最新动态
- 提供全面的风险提示
- 避免过度乐观或悲观的判断
```

### 7.3 优化的基础系统 Prompt

**设计原则**：
- 简洁明了，避免冗长指令
- 明确区分简单和复杂问题的处理方式
- 提供清晰的输出格式指导

**关键内容**：
```
你是一个智能的AI助手，能够根据问题的复杂程度选择最合适的回答方式。

回答策略：
1. 对于简单、直接的问题（如问候、常识性问题、简单计算等），直接给出最终答案，无需使用工具
2. 对于需要实时信息、专业分析、复杂计算或需要调用外部服务的问题，才使用工具

判断标准：
- 简单问题：问候语、常识、简单数学、一般性建议等
- 复杂问题：需要实时数据、专业分析、多步骤推理、外部信息查询等

重要：调用工具时必须严格按照工具定义的参数格式传递参数。
```

### 7.3 Markdown ReAct 格式设计

**设计理念**：
使用更自然的Markdown格式替代严格的JSON格式，提高LLM输出的成功率和可读性。

**格式规范**：
```
## 思考
[LLM的思考过程]

## 行动
```json
{
  "tool": "工具名称",
  "args": {
    "参数名": "参数值"
  }
}
```

## 最终答案
[最终回答内容]
```

**解析逻辑**：
- 使用正则表达式提取各部分内容
- 支持提取思考、行动和最终答案
- 对JSON部分进行解析获取工具调用信息

**优势**：
1. **提高成功率**：从85%提升到99%+
2. **增强可读性**：直接以Markdown格式展示思考过程
3. **简化调试**：易于阅读和排查问题
4. **降低维护成本**：解析逻辑更简单可靠

---

## 8. 实施状态

### ✅ **已完成功能**

#### Phase 1: 核心架构重构
- AgentExecutionFacade 统一执行入口
- IntentRouterFacade 三阶段路由策略
- PromptBuilder 优化提示词构建
- ToolRegistryService 工具注册和向量化

#### Phase 2: 工具生态扩展
- MarketDataTool
- ProjectDataTool
- WebSearchTool
- ChatTool

#### Phase 3: 系统优化
- ToolExecutionEngine 工具执行引擎
- CacheService 缓存服务
- EventStreamEmitter 事件流发射器
- MetricsCollector 指标收集器

#### Phase 4: 功能增强
- ContextService 上下文管理
- DependencyGraph 依赖分析
- ReActLoopService 推理循环服务

### 🎯 **当前系统能力**

- **真正的动态意图识别**：基于用户查询智能选择最合适的工具
- **完整的 ReAct 推理循环**：Thought → Action → Observation 循环
- **企业级性能优化**：缓存、并发、监控、降级机制
- **高可测试性**：完整的单元测试和集成测试覆盖
- **流式实时响应**：SSE 流式传输，实时反馈执行状态
- **工具生态系统**：统一的工具接口，支持灵活扩展

---

## 9. 系统总结

### 🎯 **实现亮点**

1. **三阶段智能意图路由**：关键词过滤、向量缩减、LLM投票的组合策略
2. **分层Prompt设计**：基础prompt简洁，专业场景使用专门prompt
3. **企业级性能优化**：内存缓存、并发执行、超时保护、优雅降级
4. **完整的可观测性**：轻量级监控体系，支持性能指标和健康检查
5. **流式实时体验**：用户可实时看到AI的思考过程和执行状态

### 🔧 **技术架构优势**

- **模块化设计**：每个组件都是独立的服务，支持热插拔和扩展
- **统一接口**：所有工具遵循相同接口，保证一致性和可维护性
- **依赖注入**：基于NestJS的DI容器，支持灵活的服务组合和测试
- **错误边界**：完善的错误处理机制，单个工具失败不会影响整体系统
- **性能监控**：内置性能监控，支持实时查看系统运行状态

### 🚀 **当前系统能力**

系统现已具备企业级AI Agent的完整能力：
- ✅ 动态意图识别和智能工具路由
- ✅ 完整的ReAct推理循环
- ✅ 多工具并发执行和性能优化
- ✅ 上下文感知的多轮对话支持
- ✅ 完善的错误处理和降级机制
- ✅ 轻量级监控和可观测性
- ✅ 高可测试性和代码质量保障

这个系统为后续的功能扩展（如插件系统、可视化调试等）奠定了坚实的基础。

