import { Body, Controller, Delete, Get, HttpException, HttpStatus, Logger, Post, Query, Req, Res, UseGuards, ValidationPipe, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { JwtAuthGuard } from '../auth/jwtAuth.guard';
import {
    ChatCoreMessageDto,
    ChatSessionResponseDto
} from '../dto/chatCore.dto';
import { ChatDto } from '../dto/chat.dto';
import { IntentRouterTestRequestDto, IntentRouterTestResponseDto } from '../dto/intentRouterTest.dto';
import { UserEntity } from '../entities/user.entity';
import { AgentExecutionFacade, AgentFacadeOptions } from '../service/engine/agentExecutionFacade';
import { IntentRouterFacade } from '../service/engine/intentRouterFacade.service';
import { ToolRegistryService } from '../service/engine/toolRegistry.service';
import { ChatDataService } from '../service/chat/chatData.service';
import { ModelConfigService } from '../service/llm/modelConfig.service';

// 定义带有 user 属性的请求类型
interface AuthenticatedRequest extends Request {
    user: UserEntity;
}

@ApiTags('Orchestrator')
@Controller('api/nest')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class OrchestratorController {
    private readonly logger = new Logger(OrchestratorController.name);

    constructor(
        private readonly agentFacade: AgentExecutionFacade,
        private readonly intentRouter: IntentRouterFacade,
        private readonly toolRegistry: ToolRegistryService,
        private readonly chatDataService: ChatDataService,
        private readonly modelConfigService: ModelConfigService,
    ) { }

    @Post('chat')
    @ApiOperation({ summary: '创建聊天流 (AI SDK )' })
    @ApiBody({ type: ChatDto })
    async startChatStream(
        @Body(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: false })) chatData: ChatDto,
        @Res() res: Response,
        @Req() req: AuthenticatedRequest,
    ): Promise<void> {
        try {
            const prepared = this.validateAndPrepareChatData(chatData, req.user);
            await this.persistUserRequest(prepared);

            this.setupSseHeaders(res);

            const execMode: 'simple' | 'deep' = chatData.mode === 'deep' ? 'deep' : 'simple';
            this.pipeAgentStream(prepared, req.user, res, execMode);
        } catch (err) {
            this.handleInitError(err as Error, res);
        }
    }

    private validateAndPrepareChatData(chatData: ChatDto, user: UserEntity): ChatDto {
        // 验证聊天消息是否存在
        if (!chatData.messages || chatData.messages.length === 0) {
            throw new BadRequestException('请求内容不能为空 (缺少 messages)');
        }

        // 如果没有chatId，生成一个新的UUID
        if (!chatData.id) {
            chatData.id = uuidv4();
            this.logger.log(`[ChatController] 自动生成聊天ID: ${chatData.id}`);
        }

        chatData.userId = user.id;
        this.logger.log(`[ChatController] 准备聊天流: chatId=${chatData.id}, mode=${chatData.mode}, userId=${user.id}`);
        this.logger.debug(`[ChatController] 详细聊天数据: ${JSON.stringify(chatData)}`);

        return chatData;
    }

    private async persistUserRequest(chatData: ChatDto): Promise<void> {
        const userMessage = chatData.messages![chatData.messages!.length - 1];
        await this.chatDataService.createChatSession(chatData);
        await this.chatDataService.saveUserMessage(chatData.id!, userMessage);
    }

    private setupSseHeaders(res: Response): void {
        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.status(200);
    }

    private pipeAgentStream(chatData: ChatDto, user: UserEntity, res: Response, mode: 'simple' | 'deep'): void {
        this.logger.log(`[ChatController] 开始传送 Agent Stream，模式: ${mode}`);

        const options: AgentFacadeOptions = {
            maxTurns: mode === 'deep' ? 10 : 3,
            enableCache: true, // or read from chatData if available
            chatId: chatData.id,
            userId: user.id,
        };

        const agentStream = this.agentFacade.execute(
            chatData.messages!,
            user,
            options
        );

        let fullResponse = '';

        agentStream.on('data', (chunk: Buffer) => {
            const chunkStr = chunk.toString();
            // this.logger.debug(`[ChatController] 收到数据块: ${chunkStr.trim()}`);
            // 直接将 agent service 生成的 SSE 数据块写入响应
            res.write(chunkStr);

            // 为了保存完整的助手回复，我们需要解析 SSE 数据
            if (chunkStr.startsWith('data:')) {
                try {
                    const jsonStr = chunkStr.replace(/^data:\s*/, '').trim();
                    if (jsonStr && jsonStr !== '[DONE]') {
                        const parsed = JSON.parse(jsonStr);
                        if (parsed.type === 'response') {
                            // 处理两种可能的格式：
                            // 1. data 直接是字符串: {"type":"response","data":"content"}
                            // 2. data 是对象: {"type":"response","data":{"content":"content"}}
                            const responseText = typeof parsed.data === 'string'
                                ? parsed.data
                                : parsed.data?.content || '';
                            if (responseText) {
                                fullResponse += responseText;
                                this.logger.debug(`[ChatController] 累计回复内容: ${fullResponse.substring(0, 50)}...`);
                            }
                        }
                    }
                } catch {
                    this.logger.warn(`无法解析SSE数据块: ${chunkStr}`);
                }
            }
        });

        agentStream.on('end', async () => {
            this.logger.log('[ChatController] Agent Stream 结束');
            if (fullResponse.trim() && chatData.id) {
                const assistantMessage: ChatCoreMessageDto = {
                    id: uuidv4(),
                    role: 'assistant',
                    content: fullResponse,
                    parts: [{ type: 'text', value: fullResponse }],
                    createdAt: new Date().toISOString(),
                };
                await this.chatDataService.saveUserMessage(chatData.id, assistantMessage);
            }
            // 发送一个明确的结束事件
            res.write('event: end\ndata: [DONE]\n\n');
            res.end();
        });

        agentStream.on('error', (err: any) => {
            this.logger.error(`[ChatController] Agent Stream 发生错误: ${err.message}`, err.stack);
            this.handleStreamError(err as Error, res)
        });
    }

    private handleStreamError(err: Error, res: Response) {
        this.logger.error(`AgentExecutor 流错误: ${err.message}`, err.stack);
        if (!res.headersSent) {
            res.status(500).json({ message: 'Stream error' });
        } else if (!res.destroyed) {
            // 发送一个明确的错误事件
            const errorPayload = JSON.stringify({ message: err.message });
            res.write(`event: error\ndata: ${errorPayload}\n\n`);
            res.end();
        }
    }

    private handleInitError(err: Error, res: Response) {
        this.logger.error(`聊天流初始化失败: ${err.message}`, err.stack);
        if (!res.headersSent) {
            res.status(err instanceof HttpException ? err.getStatus() : 500).json({ message: err.message });
        }
    }

    @Get('chat/session')
    @ApiOperation({ summary: '获取聊天会话信息' })
    @ApiQuery({ name: 'id', description: '聊天会話ID' })
    async getChatSession(
        @Query('id') chatId: string,
        @Req() req: AuthenticatedRequest,
    ): Promise<ChatSessionResponseDto> {
        const userId = req.user.id;
        this.logger.log(`获取聊天会话: chatId=${chatId}, userId=${userId}`);
        if (!chatId) {
            throw new HttpException('聊天会话ID不能为空', HttpStatus.BAD_REQUEST);
        }
        // 确保 chatId 为有效 UUID，避免数据库错误
        const { validate: uuidValidate } = await import('uuid');
        if (!uuidValidate(chatId)) {
            throw new HttpException('无效的聊天会话ID', HttpStatus.BAD_REQUEST);
        }
        return await this.chatDataService.getChatSession(chatId, userId);
    }

    @Delete('chat/session')
    @ApiOperation({ summary: '删除聊天会话' })
    @ApiQuery({ name: 'id', description: '聊天会话ID' })
    async deleteChatSession(
        @Query('id') chatId: string,
        @Req() req: AuthenticatedRequest,
    ): Promise<{ message: string; chatId: string }> {
        const userId = req.user.id;
        this.logger.log(`删除聊天会话: chatId=${chatId}, userId=${userId}`);
        if (!chatId) {
            throw new HttpException('聊天会话ID不能为空', HttpStatus.BAD_REQUEST);
        }
        // UUID 校验，同上
        const { validate: uuidValidate } = await import('uuid');
        if (!uuidValidate(chatId)) {
            throw new HttpException('无效的聊天会话ID', HttpStatus.BAD_REQUEST);
        }
        await this.chatDataService.deleteChatSession(chatId, userId);
        return { message: '聊天会话删除成功', chatId };
    }

    @Post('intent-router/test')
    @ApiOperation({ summary: '测试意图路由功能' })
    @ApiBody({ type: IntentRouterTestRequestDto })
    async testIntentRouter(
        @Body(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: false }))
        testData: IntentRouterTestRequestDto,
        @Req() req: AuthenticatedRequest,
    ): Promise<IntentRouterTestResponseDto> {
        try {
            this.logger.log(`[IntentRouterTest] 开始测试意图路由, query: "${testData.query}"`);

            // 获取可用工具
            const allTools = this.toolRegistry.listTools();
            let availableTools = allTools;

            // 如果指定了特定工具，则过滤
            if (testData.availableTools && testData.availableTools.length > 0) {
                availableTools = allTools.filter(tool =>
                    testData.availableTools!.includes(tool.name)
                );

                if (availableTools.length === 0) {
                    throw new BadRequestException(
                        `指定的工具不存在。可用工具: ${allTools.map(t => t.name).join(', ')}`
                    );
                }
            }

            // 构建路由选项
            const routeOptions = {
                enableCache: testData.enableCache ?? true,
                skipKeywordFilter: testData.skipKeywordFilter ?? false,
                skipVectorReduce: testData.skipVectorReduce ?? false,
                skipLlmVoting: testData.skipLlmVoting ?? true,
                vectorK: testData.vectorK ?? 10,
                debugMode: testData.debugMode ?? false,
            };

            this.logger.debug(`[IntentRouterTest] 路由选项: ${JSON.stringify(routeOptions)}`);
            this.logger.debug(`[IntentRouterTest] 可用工具: ${availableTools.map(t => t.name).join(', ')}`);

            // 执行意图路由
            const routeResult = await this.intentRouter.route(
                testData.query,
                availableTools,
                routeOptions
            );

            // 构建响应，转换工具对象为工具名称
            const response: IntentRouterTestResponseDto = {
                selectedTool: routeResult.selectedTool,
                processingStages: {
                    keyword: {
                        ...routeResult.processingStages.keyword,
                        filteredTools: routeResult.processingStages.keyword.filteredTools.map(tool =>
                            typeof tool === 'string' ? tool : tool.name
                        ),
                    },
                    vector: routeResult.processingStages.vector ? {
                        ...routeResult.processingStages.vector,
                        reducedTools: routeResult.processingStages.vector.reducedTools.map(tool =>
                            typeof tool === 'string' ? tool : tool.name
                        ),
                    } : undefined,
                    voting: routeResult.processingStages.voting ? {
                        ...routeResult.processingStages.voting,
                        votes: Object.fromEntries(routeResult.processingStages.voting.votes),
                    } : undefined,
                },
                confidence: routeResult.confidence,
                processingTime: routeResult.processingTime,
                cacheHit: routeResult.cacheHit,
                fallbackUsed: routeResult.fallbackUsed,
                availableTools: availableTools.map(tool => tool.name),
            };

            // 如果启用调试模式，添加调试信息
            if (testData.debugMode) {
                response.debugInfo = {
                    originalQuery: testData.query,
                    toolCount: availableTools.length,
                    routeOptions,
                    detailedStages: routeResult.processingStages,
                };
            }

            this.logger.log(
                `[IntentRouterTest] 路由完成, 选择工具: ${routeResult.selectedTool}, ` +
                `置信度: ${routeResult.confidence}, 处理时间: ${routeResult.processingTime}ms`
            );

            return response;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;

            this.logger.error(`[IntentRouterTest] 测试失败: ${errorMessage}`, errorStack);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new HttpException(
                `意图路由测试失败: ${errorMessage}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

}
