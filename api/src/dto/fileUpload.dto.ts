import { IsString, IsOptional, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// 本地文件存储响应类型
export interface LocalStorageResponse {
  filename: string;
  storagePath: string;
  url: string;
  size: number;
}

export class FileUploadResponseDto implements LocalStorageResponse {
  @ApiProperty({ description: '文件名' })
  filename: string;

  @ApiProperty({ description: '存储路径' })
  storagePath: string;

  @ApiProperty({ description: '文件 URL' })
  url: string;

  @ApiProperty({ description: '文件大小（字节）' })
  size: number;
}

export class FileUploadErrorDto {
  @ApiProperty({ description: '错误信息' })
  error: string;
}

export class FileUploadDto {
  @ApiProperty({ 
    type: 'string', 
    format: 'binary',
    description: '要上传的文件',
  })
  file: any;
}

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = [
  // 图片文件
  'image/jpeg', 
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
  // PDF 文件
  'application/pdf',
  // Markdown 文件
  'text/markdown',
  'text/x-markdown',
  'application/x-markdown',
  // 纯文本文件（可能包含Markdown）
  'text/plain'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB - 增加限制以支持文档文件 