import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsN<PERSON>ber, <PERSON>, Max } from 'class-validator';

/**
 * 意图路由测试请求DTO
 */
export class IntentRouterTestRequestDto {
    @ApiProperty({
        description: '用户查询内容',
        example: 'BTC最新价格是多少？',
    })
    @IsString()
    query: string;

    @ApiPropertyOptional({
        description: '可用工具范围（可选），如果不提供则使用所有已注册工具',
        example: ['chat_tool', 'market_data_tool', 'web_search_tool'],
        type: [String],
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    availableTools?: string[];

    @ApiPropertyOptional({
        description: '是否启用缓存',
        example: true,
        default: true,
    })
    @IsOptional()
    enableCache?: boolean;

    @ApiPropertyOptional({
        description: '是否跳过关键词过滤阶段',
        example: false,
        default: false,
    })
    @IsOptional()
    skipKeywordFilter?: boolean;

    @ApiPropertyOptional({
        description: '是否跳过向量缩减阶段',
        example: false,
        default: false,
    })
    @IsOptional()
    skipVectorReduce?: boolean;

    @ApiPropertyOptional({
        description: '是否跳过LLM投票阶段',
        example: true,
        default: true,
    })
    @IsOptional()
    skipLlmVoting?: boolean;

    @ApiPropertyOptional({
        description: '向量检索的K值',
        example: 10,
        minimum: 1,
        maximum: 50,
        default: 10,
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(50)
    vectorK?: number;

    @ApiPropertyOptional({
        description: '是否启用调试模式',
        example: false,
        default: false,
    })
    @IsOptional()
    debugMode?: boolean;
}

/**
 * 关键词匹配详情
 */
export interface KeywordMatchDetail {
    toolName: string;
    matchedKeywords: string[];
    score: number;
}

/**
 * 关键词过滤结果
 */
export interface KeywordFilterResult {
    filteredTools: string[];
    matchedCount: number;
    totalCandidates: number;
    filterMethod: string;
    matchDetails: KeywordMatchDetail[];
}

/**
 * 向量缩减结果
 */
export interface VectorReduceResult {
    reducedTools: string[];
    originalCount: number;
    targetCount: number;
    usedCache: boolean;
    similarities: Array<{
        toolName: string;
        similarity: number;
    }>;
    processingTime: number;
}

/**
 * LLM投票结果
 */
export interface LlmVotingResult {
    selectedTool: string | null;
    votes: { [key: string]: number };
    totalVoters: number;
    confidence: number;
    voterDetails: Array<{
        provider: string;
        model: string;
        choice: string | null;
        rawResponse: string;
        parseSuccess: boolean;
        responseTime: number;
    }>;
    processingTime: number;
}

/**
 * 意图路由测试响应DTO
 */
export class IntentRouterTestResponseDto {
    @ApiProperty({
        description: '推荐的工具名称',
        example: 'market_data_tool',
        nullable: true,
    })
    selectedTool: string | null;

    @ApiProperty({
        description: '各阶段处理结果',
        type: 'object',
        additionalProperties: true,
    })
    processingStages: {
        keyword: KeywordFilterResult;
        vector?: VectorReduceResult;
        voting?: LlmVotingResult;
    };

    @ApiProperty({
        description: '推荐工具的置信度（0-1）',
        example: 0.85,
        minimum: 0,
        maximum: 1,
    })
    confidence: number;

    @ApiProperty({
        description: '总处理时间（毫秒）',
        example: 150,
    })
    processingTime: number;

    @ApiProperty({
        description: '是否命中缓存',
        example: false,
    })
    cacheHit: boolean;

    @ApiProperty({
        description: '是否使用了降级策略',
        example: false,
    })
    fallbackUsed: boolean;

    @ApiProperty({
        description: '可用工具列表',
        example: ['chat_tool', 'market_data_tool', 'web_search_tool'],
        type: [String],
    })
    availableTools: string[];

    @ApiProperty({
        description: '调试信息（仅在调试模式下返回）',
        required: false,
    })
    debugInfo?: {
        originalQuery: string;
        toolCount: number;
        routeOptions: any;
        detailedStages: any;
    };
}
