import { Injectable, Logger } from '@nestjs/common';
import { DocumentService } from '../infra/document.service';
import { DocumentEntity, DocumentStatus } from '../../entities/document.entity';
import { LoaderFactory } from '../../loaders/loader.factory';
import { DocumentCompressor, CompressionOptions, CompressionResult } from '../../utils/documentCompressor';
import { splitText } from '../../utils/documentProcessor';

export interface ProcessingOptions {
  compressionOptions?: CompressionOptions;
  enableVectorization?: boolean;
  skipCompression?: boolean;
}

export interface ProcessingResult {
  document: DocumentEntity;
  extractedText: string;
  processedContent: string;
  compressionResult?: CompressionResult;
  chunks?: string[];
  processingTime: number;
  success: boolean;
  error?: string;
}

@Injectable()
export class DocumentProcessingService {
  private readonly logger = new Logger(DocumentProcessingService.name);
  private readonly compressor = new DocumentCompressor();

  constructor(
    private readonly documentService: DocumentService,
  ) {}

  /**
   * 处理单个文档
   */
  async processDocument(documentId: string, options: ProcessingOptions = {}): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      this.logger.log(`开始处理文档: ${documentId}`);

      // 1. 获取文档记录
      const document = await this.documentService.getDocumentById(documentId);
      if (!document) {
        throw new Error(`文档未找到: ${documentId}`);
      }

      // 2. 修正MIME类型（如果文件类型不正确）
      const correctedFileType = this.correctMimeType(document.fileType, document.filename);
      this.logger.debug(`文档MIME类型: ${document.fileType} -> ${correctedFileType}`);

      // 3. 使用LoaderFactory加载文档内容
      const loader = LoaderFactory.getLoader(correctedFileType, document.url);
      const extractedText = await loader.load();

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('文档内容为空或加载失败');
      }

      this.logger.log(`文档内容提取完成: ${extractedText.length} 字符`);

      // 4. 内容处理和压缩
      let processedContent = extractedText;
      let compressionResult: CompressionResult | undefined;

      if (!options.skipCompression && (options.compressionOptions || extractedText.length > 8000)) {
        compressionResult = await this.compressor.compress(extractedText, options.compressionOptions);
        processedContent = compressionResult.compressedText;

        this.logger.log(`文档压缩完成: ${compressionResult.originalLength} → ${compressionResult.compressedLength} 字符 (压缩率: ${Math.round(compressionResult.compressionRatio * 100)}%)`);
      }

      // 5. 文本分块（用于向量化）
      let chunks: string[] | undefined;
      if (options.enableVectorization !== false) {
        chunks = splitText(processedContent);
        this.logger.log(`文档分块完成: ${chunks.length} 个块`);
      }

      // 6. 更新文档状态为已完成
      await this.documentService.updateDocumentStatus(documentId, DocumentStatus.COMPLETED);

      const processingTime = Date.now() - startTime;
      this.logger.log(`文档处理完成: ${documentId}, 耗时: ${processingTime}ms`);

      return {
        document,
        extractedText,
        processedContent,
        compressionResult,
        chunks,
        processingTime,
        success: true,
      };

    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`文档处理失败: ${documentId}, 错误: ${error.message}`, error.stack);

      // 更新文档状态为失败
      try {
        await this.documentService.updateDocumentStatus(documentId, DocumentStatus.FAILED);
      } catch (updateError: any) {
        this.logger.error(`更新文档状态失败: ${updateError.message}`);
      }

      return {
        document: await this.documentService.getDocumentById(documentId) as DocumentEntity,
        extractedText: '',
        processedContent: '',
        processingTime,
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 批量处理文档
   */
  async processMultipleDocuments(documentIds: string[], options: ProcessingOptions = {}): Promise<ProcessingResult[]> {
    this.logger.log(`开始批量处理文档: ${documentIds.length} 个`);

    const results: ProcessingResult[] = [];

    // 并发处理（限制并发数）
    const concurrency = 3;
    for (let i = 0; i < documentIds.length; i += concurrency) {
      const batch = documentIds.slice(i, i + concurrency);
      const batchPromises = batch.map(id => this.processDocument(id, options));
      const batchResults = await Promise.allSettled(batchPromises);

      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          this.logger.error(`批量处理中的文档失败: ${result.reason}`);
          // 创建一个失败的结果
          results.push({
            document: {} as DocumentEntity,
            extractedText: '',
            processedContent: '',
            processingTime: 0,
            success: false,
            error: result.reason?.message || '未知错误',
          });
        }
      }
    }

    this.logger.log(`批量处理完成: ${results.length} 个文档`);
    return results;
  }

  /**
   * 为聊天准备文档上下文
   */
  async prepareDocumentsForChat(documentIds: string[], maxContextLength: number = 6000): Promise<string> {
    this.logger.log(`为聊天准备文档上下文: ${documentIds.length} 个文档, 最大长度: ${maxContextLength}`);

    const contexts: string[] = [];
    let totalLength = 0;

    for (const documentId of documentIds) {
      try {
        const result = await this.processDocument(documentId, {
          compressionOptions: { maxLength: Math.floor(maxContextLength / documentIds.length) },
          enableVectorization: false,
        });

        if (result.success && result.processedContent) {
          const contextText = `## 文档: ${result.document.filename}\n${result.processedContent}\n`;

          if (totalLength + contextText.length <= maxContextLength) {
            contexts.push(contextText);
            totalLength += contextText.length;
          } else {
            // 如果超出限制，截断当前文档
            const remainingLength = maxContextLength - totalLength;
            if (remainingLength > 100) { // 至少保留100字符
              const truncatedText = contextText.substring(0, remainingLength - 50) + '...\n';
              contexts.push(truncatedText);
            }
            break;
          }
        }
      } catch (error: any) {
        this.logger.warn(`准备文档上下文时跳过失败的文档: ${documentId}, 错误: ${error.message}`);
      }
    }

    const finalContext = contexts.join('\n');
    this.logger.log(`文档上下文准备完成: ${finalContext.length} 字符`);

    return finalContext;
  }

  /**
   * 获取文档处理统计信息
   */
  async getProcessingStats(documentId: string): Promise<{
    document: DocumentEntity;
    stats: any;
  } | null> {
    const document = await this.documentService.getDocumentById(documentId);
    if (!document) return null;

    // 获取文档的基本统计信息
    try {
      const loader = LoaderFactory.getLoader(document.fileType, document.url);
      const content = await loader.load();
      const stats = this.compressor.getCompressionStats(content);

      return {
        document,
        stats: {
          ...stats,
          fileSize: content.length,
          status: document.status,
          createdAt: document.createdAt,
          updatedAt: document.updatedAt,
        }
      };
    } catch (error: any) {
      this.logger.error(`获取文档统计失败: ${error.message}`);
      return {
        document,
        stats: {
          error: error.message,
          status: document.status,
        }
      };
    }
  }

  /**
   * 根据文件名修正MIME类型
   */
  private correctMimeType(fileType: string, filename: string): string {
    const fileName = filename.toLowerCase();
    
    // 如果MIME类型是application/octet-stream，根据文件扩展名判断
    if (fileType === 'application/octet-stream') {
      if (fileName.endsWith('.md') || fileName.endsWith('.markdown')) {
        return 'text/markdown';
      }
      if (fileName.endsWith('.txt')) {
        return 'text/plain';
      }
      if (fileName.endsWith('.pdf')) {
        return 'application/pdf';
      }
      if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
        return 'image/jpeg';
      }
      if (fileName.endsWith('.png')) {
        return 'image/png';
      }
      if (fileName.endsWith('.gif')) {
        return 'image/gif';
      }
      if (fileName.endsWith('.bmp')) {
        return 'image/bmp';
      }
      if (fileName.endsWith('.webp')) {
        return 'image/webp';
      }
    }
    
    return fileType;
  }
}
