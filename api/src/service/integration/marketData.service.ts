import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface MarketDataResponse {
  symbol: string;
  price: number;
  volume_24h: number;
  change_24h: number;
  change_24h_percent: number;
  high_24h: number;
  low_24h: number;
  market_cap?: number;
  timestamp: number;
}

interface KlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

@Injectable()
export class MarketDataService {
  private readonly logger = new Logger(MarketDataService.name);
  private readonly baseUrl = 'https://api.coingecko.com/api/v3';
  private readonly isMockMode: boolean;

  constructor(private readonly configService: ConfigService) {
    this.isMockMode = this.configService.get('DEFAULT_PROVIDER') === 'mock';
  }

  /**
   * 获取单个加密货币的市场数据
   */
  async getMarketData(symbol: string): Promise<MarketDataResponse> {
    if (this.isMockMode) {
      return this.getMockMarketData(symbol);
    }

    try {
      // 标准化符号 (BTC -> bitcoin, ETH -> ethereum)
      const coinId = this.symbolToCoinId(symbol);

      const response = await fetch(
        `${this.baseUrl}/simple/price?ids=${coinId}&vs_currencies=usd&include_24hr_vol=true&include_24hr_change=true&include_market_cap=true`
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();
      const coinData = data[coinId];

      if (!coinData) {
        throw new Error(`No data found for symbol: ${symbol}`);
      }

      return {
        symbol: symbol.toUpperCase(),
        price: coinData.usd,
        volume_24h: coinData.usd_24h_vol || 0,
        change_24h: coinData.usd_24h_change || 0,
        change_24h_percent: coinData.usd_24h_change || 0,
        high_24h: coinData.usd * (1 + Math.abs(coinData.usd_24h_change || 0) / 100),
        low_24h: coinData.usd * (1 - Math.abs(coinData.usd_24h_change || 0) / 100),
        market_cap: coinData.usd_market_cap,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error(`Error fetching market data for ${symbol}:`, error);
      // 降级到模拟数据
      return this.getMockMarketData(symbol);
    }
  }

  /**
   * 获取K线数据
   */
  async getKlineData(symbol: string, timeRange: string = '24h'): Promise<KlineData[]> {
    if (this.isMockMode) {
      return this.getMockKlineData(symbol, timeRange);
    }

    try {
      const coinId = this.symbolToCoinId(symbol);
      const days = this.timeRangeToDays(timeRange);

      const response = await fetch(
        `${this.baseUrl}/coins/${coinId}/market_chart?vs_currency=usd&days=${days}&interval=hourly`
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();

      return this.formatKlineData(data);
    } catch (error) {
      this.logger.error(`Error fetching kline data for ${symbol}:`, error);
      // 先尝试调用真实的K线服务，如果失败再使用mock数据
      try {
        return await this.getRealKlineData(symbol, timeRange);
      } catch (realKlineError) {
        this.logger.warn(`真实K线服务也失败，使用mock数据:`, realKlineError);
        return this.getMockKlineData(symbol, timeRange);
      }
    }
  }

  /**
   * 获取多个币种的市场数据
   */
  async getMultipleMarketData(symbols: string[]): Promise<MarketDataResponse[]> {
    const promises = symbols.map(symbol => this.getMarketData(symbol));
    return Promise.all(promises);
  }

  private symbolToCoinId(symbol: string): string {
    const symbolMap: Record<string, string> = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'BNB': 'binancecoin',
      'ADA': 'cardano',
      'SOL': 'solana',
      'XRP': 'ripple',
      'DOT': 'polkadot',
      'DOGE': 'dogecoin',
      'AVAX': 'avalanche-2',
      'MATIC': 'matic-network',
      'LINK': 'chainlink',
      'UNI': 'uniswap',
      'ATOM': 'cosmos',
      'LTC': 'litecoin',
      'BCH': 'bitcoin-cash',
    };

    // 移除 /USDT 或 -USDT 后缀，只保留币种符号
    const normalizedSymbol = symbol.replace(/[-/].*$/, '').toUpperCase();
    return symbolMap[normalizedSymbol] || normalizedSymbol.toLowerCase();
  }

  private timeRangeToDays(timeRange: string): number {
    const rangeMap: Record<string, number> = {
      '1h': 0.04,
      '4h': 0.17,
      '24h': 1,
      '7d': 7,
      '30d': 30,
      '1M': 30,
      '3M': 90,
      '1Y': 365,
    };

    return rangeMap[timeRange] || 1;
  }

  private formatKlineData(data: any): KlineData[] {
    const prices = data.prices || [];
    const volumes = data.total_volumes || [];

    return prices.map((price: number[], index: number) => {
      const volume = volumes[index] || [0, 0];
      return {
        timestamp: price[0],
        open: price[1], // 简化处理，实际应该有OHLC数据
        high: price[1] * 1.02,
        low: price[1] * 0.98,
        close: price[1],
        volume: volume[1] || 0,
      };
    });
  }

  private getMockMarketData(symbol: string): MarketDataResponse {
    const basePrice = this.getBasePriceForSymbol(symbol);
    const change = (Math.random() - 0.5) * 10; // -5% to +5%

    return {
      symbol: symbol.toUpperCase(),
      price: basePrice + (basePrice * change / 100),
      volume_24h: Math.random() * 1000000000,
      change_24h: change,
      change_24h_percent: change,
      high_24h: basePrice * 1.05,
      low_24h: basePrice * 0.95,
      market_cap: basePrice * Math.random() * 21000000,
      timestamp: Date.now(),
    };
  }

  /**
   * 调用真实的K线服务获取数据
   */
  private async getRealKlineData(
    symbol: string,
    timeRange: string,
  ): Promise<KlineData[]> {
    // 动态读取 K 线服务地址，优先级：ConfigService -> 环境变量 -> 本地默认(WslIP)
    const klineServiceUrl =
      this.configService.get<string>('MARKET_DATA_BASE_URL') ||
      process.env.MARKET_DATA_BASE_URL ||
      'http://localhost:3004';

    // 映射时间范围到interval参数（你的K线服务格式）
    const intervalMap: Record<string, string> = {
      '1h': '1h',
      '4h': '4h',
      '24h': '1h',
      '7d': '1d',
      '30d': '1d',
      '1M': '1d',
      '3M': '1d',
      '1Y': '1d',
    };

    // 映射数据点数量
    const limitMap: Record<string, number> = {
      '1h': 60,
      '4h': 24,
      '24h': 24,
      '7d': 7,
      '30d': 30,
      '1M': 30,
      '3M': 90,
      '1Y': 365,
    };

    const interval = intervalMap[timeRange] || '1h';
    const limit = limitMap[timeRange] || 24;
    
    // 智能处理 symbol，兼容主流币、小众币、币对
    let symbolFormatted: string;
    const symbolUpper = symbol.trim().toUpperCase().replace('/', '-');
    // 判断是否已经是币对格式（如 XXX-YYY）
    if (/^[A-Z0-9]+-[A-Z0-9]+$/.test(symbolUpper)) {
      symbolFormatted = symbolUpper;
    } else {
      // 只有主币种，补全为 XXX-USDT
      symbolFormatted = `${symbolUpper}-USDT`;
    }

    // 构建API URL，使用你的K线服务接口格式
    const apiUrl = new URL(`${klineServiceUrl}api/kline`);
    apiUrl.searchParams.append('symbol', symbolFormatted);
    apiUrl.searchParams.append('interval', interval);
    apiUrl.searchParams.append('limit', limit.toString());
    apiUrl.searchParams.append('sort', 'desc');

    this.logger.debug(`调用真实K线服务: ${apiUrl.toString()}`);

    const response = await fetch(apiUrl.toString());

    if (!response.ok) {
      throw new Error(`K线服务响应错误: ${response.status}`);
    }

    const result = await response.json();

    if (result.code !== 200 || !Array.isArray(result.data)) {
      throw new Error(`K线服务返回格式错误: ${result.message || '未知错误'}`);
    }

    // 转换你的K线服务格式的数据
    const klineData: KlineData[] = result.data.map((item: any) => ({
      timestamp: item.timestamp,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
      volume: item.volume,
    }));

    this.logger.debug(`成功获取${klineData.length}条真实K线数据`);
    return klineData;
  }

  private getMockKlineData(
    symbol: string,
    timeRange: string,
  ): KlineData[] {
    this.logger.debug(
      `生成 ${symbol} 在 ${timeRange} 范围内的模拟K线数据`,
    );
    return this.generateFallbackKlineData(symbol, timeRange);
  }

  // 原来的随机生成逻辑作为fallback
  private generateFallbackKlineData(
    symbol: string,
    timeRange: string,
  ): KlineData[] {
    const basePrice = this.getBasePriceForSymbol(symbol);
    const dataPoints = this.getDataPointsForTimeRange(timeRange);
    const klineData: KlineData[] = [];

    let currentPrice = basePrice;
    const now = Date.now();
    const interval = this.getIntervalForTimeRange(timeRange);

    for (let i = 0; i < dataPoints; i++) {
      const timestamp = now - (dataPoints - i - 1) * interval;
      const change = (Math.random() - 0.5) * 0.02; // 2% max change per interval

      const open = currentPrice;
      const close = open * (1 + change);
      const high = Math.max(open, close) * (1 + Math.random() * 0.01);
      const low = Math.min(open, close) * (1 - Math.random() * 0.01);

      klineData.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume: Math.random() * 1000000,
      });

      currentPrice = close;
    }

    return klineData;
  }

  private getBasePriceForSymbol(symbol: string): number {
    const priceMap: Record<string, number> = {
      'BTC': 45000,
      'ETH': 2500,
      'BNB': 300,
      'ADA': 0.5,
      'SOL': 100,
      'XRP': 0.6,
      'DOT': 7,
      'DOGE': 0.08,
      'AVAX': 35,
      'MATIC': 0.8,
    };

    const normalizedSymbol = symbol.replace(/[-/].*$/, '').toUpperCase();
    return priceMap[normalizedSymbol] || 1;
  }

  private getDataPointsForTimeRange(timeRange: string): number {
    const pointsMap: Record<string, number> = {
      '1h': 60,
      '4h': 48,
      '24h': 24,
      '7d': 168,
      '30d': 720,
      '1M': 720,
      '3M': 2160,
      '1Y': 8760,
    };

    return pointsMap[timeRange] || 24;
  }

  private getIntervalForTimeRange(timeRange: string): number {
    const intervalMap: Record<string, number> = {
      '1h': 60 * 1000, // 1 minute
      '4h': 5 * 60 * 1000, // 5 minutes
      '24h': 60 * 60 * 1000, // 1 hour
      '7d': 60 * 60 * 1000, // 1 hour
      '30d': 60 * 60 * 1000, // 1 hour
      '1M': 60 * 60 * 1000, // 1 hour
      '3M': 3 * 60 * 60 * 1000, // 3 hours
      '1Y': 24 * 60 * 60 * 1000, // 1 day
    };

    return intervalMap[timeRange] || 60 * 60 * 1000;
  }
}
