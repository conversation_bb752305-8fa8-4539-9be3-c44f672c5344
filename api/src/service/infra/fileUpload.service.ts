import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import {
  SUPPORTED_FILE_TYPES,
  MAX_FILE_SIZE
} from '../../dto/fileUpload.dto';
import { DocumentService } from './document.service';
import { DocumentEntity } from '../../entities/document.entity';
import { SimpleQueueService } from './simpleQueue.service';
import { FileStorageService } from './fileStorage.service';
// import { InjectQueue } from '@nestjs/bull';
// import { Queue } from 'bull';

// 队列名称常量（原 constants 文件合并）
export const FILE_PROCESSING_QUEUE = 'file-processing';

// import { Processor, Process } from '@nestjs/bull';
// import { Job } from 'bull';
import { LoaderFactory } from '../../loaders/loader.factory';
import { DocumentStatus } from '../../entities/document.entity';

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

export interface FileProcessingJob {
  documentId: string;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);

  constructor(
    private readonly documentService: DocumentService,
    private readonly simpleQueueService: SimpleQueueService,
    private readonly fileStorageService: FileStorageService,
    // @InjectQueue(FILE_PROCESSING_QUEUE) private readonly queue: Queue,
  ) {}

  /**
   * 验证文件类型和大小
   * @param file 上传的文件
   */
  private validateFile(file: UploadedFile): void {
    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      throw new HttpException(
        'File size should be less than 10MB',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 调试：记录文件信息
    this.logger.debug(`文件验证 - 文件名: ${file.originalname}, MIME类型: ${file.mimetype}, 大小: ${file.size}`);

    // 检查文件类型
    if (!SUPPORTED_FILE_TYPES.includes(file.mimetype)) {
      // 特殊处理：检查是否为Markdown文件但MIME类型不正确
      const fileName = file.originalname.toLowerCase();
      if (fileName.endsWith('.md') || fileName.endsWith('.markdown')) {
        this.logger.warn(`Markdown文件被识别为错误的MIME类型: ${file.mimetype}, 允许上传`);
        return; // 允许上传
      }
      
      this.logger.error(`不支持的文件类型: ${file.mimetype}, 支持的类型: ${SUPPORTED_FILE_TYPES.join(', ')}`);
      throw new HttpException(
        `Unsupported file type. Supported types are: ${SUPPORTED_FILE_TYPES.join(', ')}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 上传文件到本地存储并创建数据库记录
   * @param file 上传的文件
   * @param userId 用户ID（可选）
   * @returns 创建的 DocumentEntity 包含存储信息
   */
  async uploadFile(
    file: UploadedFile,
    userId?: string,
  ): Promise<DocumentEntity & { size: number }> {
    try {
      this.logger.log(`开始上传文件: ${file.originalname}${userId ? `, 用户ID: ${userId}` : ' (匿名用户)'}`);

      // 1. 验证文件
      this.validateFile(file);

      // 2. 保存到本地存储
      const storageResult = await this.fileStorageService.saveFile(
        file.buffer,
        file.originalname
      );

      this.logger.log(`文件保存到本地成功: ${storageResult.url}`);

      // 3. 在数据库中创建文档记录
      const documentRecord = await this.documentService.createDocumentFromFile({
        userId,
        filename: file.originalname,
        storagePath: storageResult.storagePath,
        url: storageResult.url,
        fileType: file.mimetype,
      });

      // 4. 将处理任务添加到简单队列
      try {
        const jobId = await this.simpleQueueService.addDocumentProcessingJob(documentRecord.id);
        this.logger.log(`文档处理任务已添加到队列: ${jobId}, 文档ID: ${documentRecord.id}`);
      } catch (queueError: any) {
        this.logger.warn(`队列添加失败，但文件上传成功: ${queueError.message}`);
        // 队列失败不影响文件上传成功
      }

      // 返回包含size信息的完整响应
      return {
        ...documentRecord,
        size: storageResult.size,
      };
    } catch (error: any) {
      this.logger.error(`文件上传失败: ${error?.message || error}`, error?.stack);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException('Upload failed', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 批量上传文件
   * @param files 上传的文件数组
   * @param userId 用户ID（可选）
   * @returns DocumentEntity 数组
   */
  async uploadMultipleFiles(
    files: UploadedFile[],
    userId?: string,
  ): Promise<DocumentEntity[]> {
    this.logger.log(`开始批量上传 ${files.length} 个文件${userId ? `, 用户ID: ${userId}` : ' (匿名用户)'}`);
    const uploadPromises = files.map((file) => this.uploadFile(file, userId));
    return Promise.all(uploadPromises);
  }

  /**
   * 获取文件上传限制信息
   */
  getUploadLimits() {
    return {
      maxFileSize: MAX_FILE_SIZE,
      maxFileSizeMB: MAX_FILE_SIZE / (1024 * 1024),
      supportedTypes: SUPPORTED_FILE_TYPES,
    };
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return this.simpleQueueService.getQueueStatus();
  }
}

// @Processor(FILE_PROCESSING_QUEUE)
// export class FileProcessingConsumer {
//   private readonly logger = new Logger(FileProcessingConsumer.name);

//   constructor(private readonly documentService: DocumentService) {}

//   @Process()
//   async process(job: Job<FileProcessingJob>) {
//     const { documentId } = job.data;
//     this.logger.log(`Processing job for document ${documentId}...`);
//     try {
//       const document = await this.documentService.getDocumentById(documentId);
//       if (!document) throw new Error('Document not found');

//       const loader = LoaderFactory.getLoader(document.fileType, document.url);
//       const text = await loader.load();

//       this.logger.log(`Extracted text length=${text.length}`);

//       await this.documentService.updateDocumentStatus(documentId, DocumentStatus.COMPLETED);
//     } catch (error: any) {
//       await this.documentService.updateDocumentStatus(documentId, DocumentStatus.FAILED);
//       this.logger.error(`Failed processing ${documentId}: ${error.message}`);
//       throw error;
//     }
//   }
// }
