import { Injectable, Logger } from '@nestjs/common';
import { UserRole } from '../types/role.types';
import { ITool } from '../../../tools/tool.interface';
import { ToolExecutionEngine } from '../toolExecutionEngine';

/**
 * 工作流步骤结果
 */
export interface WorkflowStepResult {
    stepName: string;
    toolUsed?: string;
    result: any;
    success: boolean;
    error?: string;
    duration: number;
}

/**
 * 工作流执行结果
 */
export interface WorkflowExecutionResult {
    role: UserRole;
    query: string;
    steps: WorkflowStepResult[];
    finalResult: any;
    totalDuration: number;
    success: boolean;
}

/**
 * 角色工作流服务
 * 为不同角色提供专门的工作流程，确保从互联网搜索最新信息并结合分析
 */
@Injectable()
export class RoleWorkflowService {
    private readonly logger = new Logger(RoleWorkflowService.name);

    constructor(private readonly toolExecutionEngine: ToolExecutionEngine) {}

    /**
     * 执行角色特定的工作流
     */
    async executeRoleWorkflow(
        role: UserRole,
        query: string,
        tools: ITool[]
    ): Promise<WorkflowExecutionResult> {
        const startTime = Date.now();
        const steps: WorkflowStepResult[] = [];

        this.logger.debug(`[RoleWorkflowService] 开始执行${role}工作流，查询: "${query}"`);

        try {
            let finalResult: any;

            switch (role) {
                case UserRole.RESEARCHER:
                    finalResult = await this.executeResearcherWorkflow(query, tools, steps);
                    break;
                case UserRole.MARKET_ANALYST:
                    finalResult = await this.executeMarketAnalystWorkflow(query, tools, steps);
                    break;
                case UserRole.PROJECT_ANALYST:
                    finalResult = await this.executeProjectAnalystWorkflow(query, tools, steps);
                    break;
                case UserRole.GENERAL_CHAT:
                default:
                    finalResult = await this.executeGeneralChatWorkflow(query, tools, steps);
                    break;
            }

            return {
                role,
                query,
                steps,
                finalResult,
                totalDuration: Date.now() - startTime,
                success: true,
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`[RoleWorkflowService] 工作流执行失败: ${errorMessage}`, errorStack);
            return {
                role,
                query,
                steps,
                finalResult: null,
                totalDuration: Date.now() - startTime,
                success: false,
            };
        }
    }

    /**
     * 研究员工作流
     */
    private async executeResearcherWorkflow(
        query: string,
        tools: ITool[],
        steps: WorkflowStepResult[]
    ): Promise<any> {
        // 1. 搜索相关信息和最新动态
        const searchResult = await this.executeStep(
            '搜索相关信息',
            'WebSearchTool',
            { query },
            tools,
            steps
        );

        // 2. 抓取重要网页内容进行深度分析
        const fetchResults = [];
        if (searchResult && searchResult.results) {
            const topUrls = searchResult.results.slice(0, 3).map((r: any) => r.url);
            for (const url of topUrls) {
                const fetchResult = await this.executeStep(
                    `抓取网页内容: ${url}`,
                    'FetchUrlTool',
                    { url },
                    tools,
                    steps
                );
                if (fetchResult) {
                    fetchResults.push(fetchResult);
                }
            }
        }

        // 3. 整合多源信息，形成综合报告
        const summaryData = {
            query,
            searchResults: searchResult,
            fetchedContent: fetchResults,
        };

        const summaryResult = await this.executeStep(
            '整合信息生成研究报告',
            'SummarizeTextTool',
            { text: JSON.stringify(summaryData), maxLength: 2000 },
            tools,
            steps
        );

        return {
            type: 'research_report',
            query,
            searchResults: searchResult,
            detailedContent: fetchResults,
            summary: summaryResult,
            methodology: '系统性研究方法：广泛搜索 → 深度分析 → 综合整理',
        };
    }

    /**
     * 行情分析师工作流
     */
    private async executeMarketAnalystWorkflow(
        query: string,
        tools: ITool[],
        steps: WorkflowStepResult[]
    ): Promise<any> {
        // 1. 获取实时市场数据
        const marketDataResult = await this.executeStep(
            '获取市场数据',
            'MarketDataTool',
            { query },
            tools,
            steps
        );

        // 2. 搜索最新市场动态和新闻
        const newsSearchResult = await this.executeStep(
            '搜索市场新闻',
            'WebSearchTool',
            { query: `${query} 最新新闻 市场动态` },
            tools,
            steps
        );

        // 3. 查看专家观点和市场情绪
        const expertSearchResult = await this.executeStep(
            '搜索专家观点',
            'WebSearchTool',
            { query: `${query} 专家分析 投资建议` },
            tools,
            steps
        );

        return {
            type: 'market_analysis',
            query,
            marketData: marketDataResult,
            latestNews: newsSearchResult,
            expertOpinions: expertSearchResult,
            analysisFramework: '技术分析 + 基本面分析 + 市场情绪',
            riskWarning: '投资有风险，决策需谨慎。本分析仅供参考，不构成投资建议。',
        };
    }

    /**
     * 项目分析师工作流
     */
    private async executeProjectAnalystWorkflow(
        query: string,
        tools: ITool[],
        steps: WorkflowStepResult[]
    ): Promise<any> {
        // 1. 获取项目基础数据
        const projectDataResult = await this.executeStep(
            '获取项目数据',
            'ProjectDataTool',
            { query },
            tools,
            steps
        );

        // 2. 搜索项目相关新闻和社区讨论
        const projectNewsResult = await this.executeStep(
            '搜索项目新闻',
            'WebSearchTool',
            { query: `${query} 项目进展 最新动态` },
            tools,
            steps
        );

        // 3. 分析项目文档和技术资料
        const techAnalysisResult = await this.executeStep(
            '分析技术文档',
            'DocumentAnalysisTool',
            { query: `${query} 白皮书 技术文档` },
            tools,
            steps
        );

        // 4. 评估团队背景和项目进展
        const teamAnalysisResult = await this.executeStep(
            '搜索团队信息',
            'WebSearchTool',
            { query: `${query} 团队背景 创始人 合作伙伴` },
            tools,
            steps
        );

        return {
            type: 'project_analysis',
            query,
            projectData: projectDataResult,
            latestNews: projectNewsResult,
            technicalAnalysis: techAnalysisResult,
            teamAnalysis: teamAnalysisResult,
            evaluationDimensions: [
                '技术创新性',
                '代币经济学',
                '团队实力',
                '市场竞争力',
                '发展前景',
                '风险评估'
            ],
        };
    }

    /**
     * 通用对话工作流
     */
    private async executeGeneralChatWorkflow(
        query: string,
        tools: ITool[],
        steps: WorkflowStepResult[]
    ): Promise<any> {
        // 对于通用对话，通常不需要复杂的工作流
        // 如果需要外部信息，可以进行简单搜索
        const needsSearch = this.shouldSearchForGeneralChat(query);

        if (needsSearch) {
            const searchResult = await this.executeStep(
                '搜索相关信息',
                'WebSearchTool',
                { query },
                tools,
                steps
            );

            return {
                type: 'general_chat_with_search',
                query,
                searchResult,
                approach: '基于搜索结果的知识增强对话',
            };
        }

        return {
            type: 'general_chat',
            query,
            approach: '基于内置知识的直接对话',
        };
    }

    /**
     * 执行单个工作流步骤
     */
    private async executeStep(
        stepName: string,
        toolName: string,
        args: any,
        tools: ITool[],
        steps: WorkflowStepResult[]
    ): Promise<any> {
        const startTime = Date.now();
        
        try {
            const tool = tools.find(t => t.name === toolName);
            if (!tool) {
                throw new Error(`工具 ${toolName} 未找到`);
            }

            // 直接调用工具的execute方法，而不是通过toolExecutionEngine
            const result = await tool.execute(args);
            
            const stepResult: WorkflowStepResult = {
                stepName,
                toolUsed: toolName,
                result,
                success: true,
                duration: Date.now() - startTime,
            };
            
            steps.push(stepResult);
            this.logger.debug(`[RoleWorkflowService] 步骤完成: ${stepName}`);
            
            return result;
        } catch (error) {
            const stepResult: WorkflowStepResult = {
                stepName,
                toolUsed: toolName,
                result: null,
                success: false,
                error: error instanceof Error ? error.message : String(error),
                duration: Date.now() - startTime,
            };
            
            steps.push(stepResult);
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.warn(`[RoleWorkflowService] 步骤失败: ${stepName}, 错误: ${errorMessage}`);
            
            return null;
        }
    }

    /**
     * 判断通用对话是否需要搜索
     */
    private shouldSearchForGeneralChat(query: string): boolean {
        const searchKeywords = [
            '最新', '现在', '今天', '最近', '当前',
            '新闻', '动态', '发生', '事件', '情况',
            '价格', '行情', '股价', '汇率'
        ];

        return searchKeywords.some(keyword => query.includes(keyword));
    }
}
