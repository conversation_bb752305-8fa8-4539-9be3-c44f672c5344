import { Injectable, Logger } from '@nestjs/common';
import { PassThrough } from 'stream';
import { randomUUID } from 'crypto';

/**
 * 事件元数据
 */
export interface EventMeta {
    correlationId: string;
    requestId: string;
    sessionId?: string;
    timestamp?: number;
}

/**
 * 流事件接口
 */
export interface StreamEvent {
    type: string;
    data: any;
    timestamp: number;
    correlationId: string;
    requestId: string;
}

/**
 * 事件流发射器
 * 负责向客户端发送各种类型的事件
 */
@Injectable()
export class EventStreamEmitter {
    private readonly logger = new Logger(EventStreamEmitter.name);

    constructor() {}

    /**
     * 安全的JSON序列化，避免循环引用
     */
    private safeStringify(obj: any): string {
        const seen = new WeakSet();
        return JSON.stringify(obj, (key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) {
                    return '[Circular]';
                }
                seen.add(value);
                
                // 处理TypeORM实体
                if (value.constructor && value.constructor.name === 'EntityMetadata') {
                    return '[EntityMetadata]';
                }
                
                // 处理其他可能的循环引用对象
                if (value.constructor && value.constructor.name.includes('Metadata')) {
                    return `[${value.constructor.name}]`;
                }
            }
            return value;
        });
    }

    /**
     * 发射事件
     */
    emit(
        stream: PassThrough,
        type: string,
        data: any,
        correlationId: string,
        requestId: string
    ): void {
        const event: StreamEvent = {
            type,
            data,
            timestamp: Date.now(),
            correlationId,
            requestId
        };

        const eventJson = this.safeStringify(event);
        stream.write(`data: ${eventJson}\n\n`);

        this.logger.debug(`[EventStreamEmitter] 发射事件: ${type}, correlationId: ${correlationId}`);
    }

    /**
     * 发射思考过程事件
     */
    emitThought(
        stream: PassThrough,
        content: string,
        step: number,
        reasoning: string,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'thought', {
            content,
            step,
            reasoning
        }, correlationId, requestId);
    }

    /**
     * 发射LLM调用开始事件
     */
    emitLlmStart(
        stream: PassThrough,
        provider: string,
        model: string,
        callId: string,
        callType: 'reasoning' | 'tool_selection' | 'final_answer',
        correlationId: string,
        requestId: string,
        additionalData?: any
    ): void {
        this.emit(stream, 'llm_start', {
            provider,
            model,
            callId,
            callType,
            startTime: Date.now(),
            ...additionalData
        }, correlationId, requestId);
    }

    /**
     * 发射LLM调用结束事件
     */
    emitLlmEnd(
        stream: PassThrough,
        provider: string,
        model: string,
        callId: string,
        duration: number,
        tokenCount: number,
        correlationId: string,
        requestId: string,
        additionalData?: any
    ): void {
        this.emit(stream, 'llm_end', {
            provider,
            model,
            callId,
            callType: 'reasoning' as const,
            duration,
            tokenCount,
            endTime: Date.now(),
            ...additionalData
        }, correlationId, requestId);
    }

    /**
     * 发射工具执行开始事件
     */
    emitToolStart(
        stream: PassThrough,
        toolName: string,
        toolId: string,
        input: any,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'tool_start', {
            name: toolName,  // 保持与原有格式兼容
            toolName,
            toolId,
            input
        }, correlationId, requestId);
    }

    /**
     * 发射工具执行结束事件
     */
    emitToolEnd(
        stream: PassThrough,
        toolName: string,
        toolId: string,
        output: any,
        executionTime: number,
        fromCache: boolean,
        correlationId: string,
        requestId: string,
        error?: string
    ): void {
        this.emit(stream, 'tool_end', {
            name: toolName,  // 保持与原有格式兼容
            toolName,
            toolId,
            output: error ? undefined : output,
            error,
            executionTime,
            fromCache
        }, correlationId, requestId);
    }

    /**
     * 发射工具执行错误事件
     */
    emitToolError(
        stream: PassThrough,
        toolName: string,
        toolId: string,
        error: string,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'tool_error', {
            name: toolName,  // 保持与原有格式兼容
            toolName,
            toolId,
            error
        }, correlationId, requestId);
    }

    /**
     * 发射并发工具执行开始事件
     */
    emitConcurrentStart(
        stream: PassThrough,
        batchId: string,
        tools: string[],
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'concurrent_tools_start', {
            batchId,
            tools,
            count: tools.length,
            totalCount: tools.length
        }, correlationId, requestId);
    }

    /**
     * 发射并发工具执行结束事件
     */
    emitConcurrentEnd(
        stream: PassThrough,
        batchId: string,
        completedCount: number,
        totalCount: number,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'concurrent_tools_end', {
            batchId,
            completedCount,
            totalCount
        }, correlationId, requestId);
    }

    /**
     * 发射Agent执行开始事件
     */
    emitAgentStart(
        stream: PassThrough,
        data: any,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'agent_start', data, correlationId, requestId);
    }

    /**
     * 发射Agent执行结束事件
     */
    emitAgentEnd(
        stream: PassThrough,
        data: any,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'agent_end', data, correlationId, requestId);
    }

    /**
     * 发射响应事件
     */
    emitResponse(
        stream: PassThrough,
        content: string,
        correlationId: string,
        requestId: string,
        isComplete: boolean = false
    ): void {
        this.emit(stream, 'response', {
            content,
            isComplete
        }, correlationId, requestId);
    }

    /**
     * 发射错误事件
     */
    emitError(
        stream: PassThrough,
        message: string,
        correlationId: string,
        requestId: string,
        code?: string,
        details?: any
    ): void {
        this.emit(stream, 'error', {
            message,
            code,
            details
        }, correlationId, requestId);
    }

    /**
     * 发射依赖分析完成事件
     */
    emitDependencyResolved(
        stream: PassThrough,
        data: any,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, 'dependency_resolved', data, correlationId, requestId);
    }

    /**
     * 发射工具执行结果事件 (兼容原ToolExecutionResult)
     */
    emitToolResult(
        stream: PassThrough,
        result: any, // ToolExecutionResult from execution engine
        correlationId: string,
        requestId: string
    ): void {
        if (result.success) {
            this.emitToolEnd(stream, result.toolName, result.taskId, result.result, result.executionTime, result.fromCache, correlationId, requestId);
        } else {
            this.emitToolError(stream, result.toolName, result.taskId, result.error, correlationId, requestId);
        }
    }

    /**
     * 生成关联ID
     */
    generateCorrelationId(): string {
        return randomUUID().substring(0, 8);
    }

    /**
     * 生成请求ID
     */
    generateRequestId(): string {
        return randomUUID().substring(0, 8);
    }

    /**
     * 兼容性方法：支持原有的emitEvent调用方式
     */
    emitEvent(
        stream: PassThrough,
        type: string,
        data: any,
        correlationId: string,
        requestId: string
    ): void {
        this.emit(stream, type, data, correlationId, requestId);
    }
}
