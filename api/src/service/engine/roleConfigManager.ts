import { Injectable } from '@nestjs/common';
import { UserRole, RoleConfig } from './types/role.types';

/**
 * 角色配置管理器
 * 管理所有角色的配置信息，包括关键词、工具组合和提示词
 */
@Injectable()
export class RoleConfigManager {
    private readonly roleConfigs: Map<UserRole, RoleConfig> = new Map();

    constructor() {
        this.initializeRoleConfigs();
    }

    /**
     * 初始化所有角色配置
     */
    private initializeRoleConfigs(): void {
        // 通用对话角色
        this.roleConfigs.set(UserRole.GENERAL_CHAT, {
            name: '通用对话',
            description: '处理日常对话、知识问答、创意写作等通用场景',
            keywords: [
                '你好', '帮我写', '解释', '什么是', '如何', '为什么',
                '创意', '写作', '故事', '诗歌', '建议', '推荐',
                '聊天', '对话', '问候', '谢谢', '再见'
            ],
            recommendedTools: ['ChatTool'],
            systemPrompt: `你是一个友好、博学的AI助手，擅长日常对话和知识问答。

核心特点：
- 语言自然流畅，富有亲和力
- 知识面广泛，能够解答各类常识问题
- 善于创意写作和头脑风暴
- 提供实用的生活建议和指导

回答风格：
- 直接、简洁、易懂
- 适当使用比喻和例子
- 保持积极正面的态度
- 鼓励用户进一步思考和探索`,
            workflow: [
                '理解用户的问题或需求',
                '基于知识库提供准确回答',
                '适当补充相关信息和建议',
                '鼓励用户进一步交流'
            ]
        });

        // 研究员角色
        this.roleConfigs.set(UserRole.RESEARCHER, {
            name: '研究员',
            description: '深度研究和信息整合，提供全面的主题分析',
            keywords: [
                '研究', '调研', '分析', '报告', '综合', '整理',
                '深入了解', '详细分析', '全面研究', '调查',
                '资料', '信息', '数据', '统计', '趋势'
            ],
            recommendedTools: ['WebSearchTool', 'FetchUrlTool', 'SummarizeTextTool', 'ChatTool'],
            systemPrompt: `你是一位专业的研究员，擅长深度调研和信息整合。

核心能力：
- 系统性思维，能够从多个角度分析问题
- 信息收集和筛选能力强
- 善于发现信息之间的关联和趋势
- 能够提供客观、全面的研究报告

工作方法：
1. 明确研究目标和范围
2. 广泛收集相关信息和数据
3. 深入分析关键内容和观点
4. 整合多源信息，形成结构化报告
5. 提供基于证据的结论和建议

重要原则：
- 始终从互联网搜索最新、最权威的信息
- 引用可靠来源，标注信息出处
- 保持客观中立，避免主观偏见
- 提供多角度分析，展示不同观点`,
            workflow: [
                '理解研究主题和范围',
                '搜索相关信息和最新动态',
                '抓取重要网页内容进行深度分析',
                '整合多源信息，形成综合报告',
                '提供结构化的研究结论和建议'
            ]
        });

        // 行情分析师角色
        this.roleConfigs.set(UserRole.MARKET_ANALYST, {
            name: '行情分析师',
            description: '提供专业的市场分析和投资洞察',
            keywords: [
                '价格', '行情', '涨跌', '市值', '交易量', 'K线', '趋势',
                '投资', '买入', '卖出', '持有', '分析', '预测',
                '币价', '汇率', '股价', '指数', '技术分析', '基本面'
            ],
            recommendedTools: ['MarketDataTool', 'WebSearchTool', 'ChatTool'],
            systemPrompt: `你是一位专业的行情分析师，具备深厚的金融市场知识和分析能力。

专业领域：
- 加密货币市场分析
- 技术分析和基本面分析
- 市场趋势预测和风险评估
- 投资策略和资产配置建议

分析框架：
1. 获取最新的市场数据和价格信息
2. 搜索相关新闻、事件和市场动态
3. 分析技术指标和价格走势
4. 评估市场情绪和资金流向
5. 结合基本面因素给出综合判断

专业要求：
- 必须使用最新的市场数据
- 结合多个信息源进行分析
- 明确标注数据来源和时间
- 提供风险提示和免责声明
- 避免给出绝对的投资建议

输出格式：
- 当前价格和关键指标
- 技术分析和趋势判断
- 基本面分析和影响因素
- 风险评估和注意事项`,
            workflow: [
                '获取实时市场数据和价格信息',
                '搜索最新市场动态和新闻',
                '查看专家观点和市场情绪',
                '分析技术指标和价格趋势',
                '综合分析给出专业投资建议'
            ]
        });

        // 项目分析师角色
        this.roleConfigs.set(UserRole.PROJECT_ANALYST, {
            name: '项目分析师',
            description: '提供深度的项目分析和技术评估',
            keywords: [
                '项目', '协议', '代币', '白皮书', '团队', '技术', '机制',
                '风险', '评估', '分析', 'DeFi', 'NFT', 'GameFi',
                '智能合约', '区块链', '生态', '路线图', '合作伙伴'
            ],
            recommendedTools: ['ProjectDataTool', 'WebSearchTool', 'DocumentAnalysisTool', 'ChatTool'],
            systemPrompt: `你是一位专业的项目分析师，专注于区块链和加密货币项目的深度分析。

核心能力：
- 项目技术架构分析
- 代币经济学评估
- 团队背景和项目进展跟踪
- 竞争格局和市场定位分析
- 风险识别和投资价值评估

分析维度：
1. 项目基本信息和发展历程
2. 技术特点和创新亮点
3. 代币机制和经济模型
4. 团队背景和合作伙伴
5. 市场表现和社区活跃度
6. 竞争优势和潜在风险

工作流程：
1. 收集项目官方信息和白皮书
2. 搜索最新的项目动态和新闻
3. 分析项目技术文档和代码
4. 评估团队实力和执行能力
5. 对比竞争项目和市场地位
6. 综合评估投资价值和风险

重要原则：
- 基于事实和数据进行分析
- 保持客观中立的立场
- 及时更新项目最新动态
- 提供全面的风险提示
- 避免过度乐观或悲观的判断`,
            workflow: [
                '获取项目基础数据和代币信息',
                '搜索项目相关新闻和社区讨论',
                '分析项目文档、白皮书和技术资料',
                '评估团队背景和项目进展',
                '综合评估项目价值、风险和前景'
            ]
        });
    }

    /**
     * 获取角色配置
     */
    getRoleConfig(role: UserRole): RoleConfig | undefined {
        return this.roleConfigs.get(role);
    }

    /**
     * 获取所有角色配置
     */
    getAllRoleConfigs(): Map<UserRole, RoleConfig> {
        return new Map(this.roleConfigs);
    }

    /**
     * 根据关键词匹配角色
     */
    matchRoleByKeywords(query: string): Array<{ role: UserRole; score: number; matchedKeywords: string[] }> {
        const results: Array<{ role: UserRole; score: number; matchedKeywords: string[] }> = [];
        const queryLower = query.toLowerCase();

        for (const [role, config] of this.roleConfigs) {
            const matchedKeywords: string[] = [];
            let score = 0;

            for (const keyword of config.keywords) {
                if (queryLower.includes(keyword.toLowerCase())) {
                    matchedKeywords.push(keyword);
                    score += 1;
                }
            }

            if (matchedKeywords.length > 0) {
                // 计算匹配度分数：使用原始分数而不是归一化，避免分数过低
                results.push({ role, score, matchedKeywords });
            }
        }

        // 按分数降序排序，分数相同时按角色优先级排序
        return results.sort((a, b) => {
            if (b.score !== a.score) {
                return b.score - a.score;
            }
            // 分数相同时，专业角色优先于通用对话
            const roleOrder = {
                [UserRole.MARKET_ANALYST]: 3,
                [UserRole.PROJECT_ANALYST]: 2,
                [UserRole.RESEARCHER]: 1,
                [UserRole.GENERAL_CHAT]: 0,
        };
            return roleOrder[b.role] - roleOrder[a.role];
        });
    }

    /**
     * 获取角色的推荐工具
     */
    getRoleTools(role: UserRole): string[] {
        const config = this.getRoleConfig(role);
        return config ? config.recommendedTools : [];
    }

    /**
     * 获取角色的系统提示词
     */
    getRolePrompt(role: UserRole): string {
        const config = this.getRoleConfig(role);
        return config ? config.systemPrompt : '';
    }
}
