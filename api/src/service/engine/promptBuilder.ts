import { Injectable } from '@nestjs/common';
import { ITool } from '../../tools/tool.interface';
import { IChatInputMessage } from '../../interface';
import { ConversationContext, ContextService } from './contextService';
import { PromptTemplate } from '@langchain/core/prompts';
import { ModelConfigService } from '../llm/modelConfig.service';
import { LlmService } from '../llm/llm.service';
import { UserRole } from './types/role.types';
import { RoleConfigManager } from './roleConfigManager';

/**
 * Prompt构建选项
 */
export interface PromptBuilderOptions {
    includeContextHint?: boolean;
    useXMLTags?: boolean;
    toolFormat?: 'json' | 'list' | 'xml';
    maxToolDescriptionLength?: number;
    customSystemPrompt?: string;
    role?: UserRole;
    rolePrompt?: string;
}

/**
 * Prompt构建器服务
 * 负责根据上下文、可用工具和历史消息动态构建ReAct风格的Prompt
 */
@Injectable()
export class PromptBuilder {

    private readonly defaultSystemPrompt = `你是一个智能的AI助手，能够根据问题的复杂程度选择最合适的回答方式。

回答策略：
1. 对于简单、直接的问题（如问候、常识性问题、简单计算等），直接给出<final_answer>，无需使用工具
2. 对于需要实时信息、专业分析、复杂计算或需要调用外部服务的问题，才使用工具

判断标准：
- 简单问题：问候语、常识、简单数学、一般性建议等
- 复杂问题：需要实时数据、专业分析、多步骤推理、外部信息查询等

重要：识别用户上传的文档：
- 当用户上传了文档文件并询问文档内容时，应该使用document_analysis工具进行分析，而不是直接回答。
- 如果用户消息中包含experimental_attachments字段，说明用户已经上传了文档文件
- 这些附件就是需要分析的文档，不需要用户再次上传
- 当用户说"分析文档"、"查看文档"、"文档内容"等时，如果存在附件，应该使用document_analysis工具

重要：调用工具时必须严格按照工具定义的参数格式传递参数。每个工具的参数格式都在工具描述中列出。

请根据用户问题的复杂程度，选择以下响应格式之一：
- 简单问题：<final_answer>直接回答</final_answer>
- 复杂问题：<thought>分析问题并选择工具</thought><action tool="工具名" args='{"参数名": "参数值"}' />

在<action>标签中，args参数必须是有效的JSON格式，并且参数名称必须与工具定义完全匹配。`;

    constructor(private readonly roleConfigManager: RoleConfigManager) {}

    /**
     * 构建角色特定的系统提示词
     */
    buildRolePrompt(role?: UserRole, customPrompt?: string): string {
        if (customPrompt) {
            return customPrompt;
        }

        if (role) {
            const rolePrompt = this.roleConfigManager.getRolePrompt(role);
            if (rolePrompt) {
                return rolePrompt;
            }
        }

        return this.defaultSystemPrompt;
    }

    /**
     * 构建ReAct Prompt
     */
    build(
        query: string,
        history: IChatInputMessage[],
        tools: ITool[],
        context: ConversationContext,
        thoughts: string[],
        options: PromptBuilderOptions = {}
    ): IChatInputMessage[] {
        const systemPrompt = this.buildSystemPrompt(tools, context, options);

        const messages: IChatInputMessage[] = [{ role: 'system', content: systemPrompt }];

        // 添加历史消息
        messages.push(...history);

        // 检查当前用户消息是否包含附件，如果有则增强查询
        const currentUserMessage = history[history.length - 1];
        let enhancedQuery = query;
        // 检查消息是否有附件（通过any类型来访问可能存在的experimental_attachments属性）
        const messageWithAttachments = currentUserMessage as any;
        if (messageWithAttachments?.experimental_attachments && messageWithAttachments.experimental_attachments.length > 0) {
            enhancedQuery = `${query}\n\n用户已上传文档附件，请使用document_analysis工具分析文档内容。`;
        }
        // 如果没有附件，enhancedQuery保持为原始的query

        // 添加当前查询
        messages.push({ role: 'user', content: enhancedQuery });

        // 添加已有的思考和行动历史
        if (thoughts.length > 0) {
            messages.push({ role: 'assistant', content: thoughts.join('\n') });
        }

        return messages;
    }

    /**
     * 构建系统提示
     */
    private buildSystemPrompt(
        tools: ITool[],
        context: ConversationContext,
        options: PromptBuilderOptions
    ): string {
        // 优先使用角色特定的提示词
        const basePrompt = this.buildRolePrompt(options.role, options.customSystemPrompt);

        const toolList = this.formatTools(tools, options);
        const contextHint = options.includeContextHint ? this.formatContext(context) : '';

        return `${basePrompt}\n\n${contextHint}\n\n可用工具列表:\n${toolList}\n\n响应格式:\n<thought>你的思考过程</thought>\n<action tool="工具名" args='{"参数名": "参数值"}' />\n或者\n<final_answer>最终答案</final_answer>\n\n注意：args中的参数名必须与工具定义中的参数名完全一致，参数值必须符合工具定义的类型要求。`;
    }

    /**
     * 格式化工具列表
     */
    private formatTools(tools: ITool[], options: PromptBuilderOptions): string {
        const format = options.toolFormat || 'list';
        const maxLength = options.maxToolDescriptionLength || 100;

        const toolStrings = tools.map(tool => {
            const desc = tool.description.length > maxLength
                ? `${tool.description.substring(0, maxLength)}...`
                : tool.description;

            // 添加参数信息
            const schemaInfo = this.formatToolSchema(tool);

            switch(format) {
                case 'json':
                    return JSON.stringify({
                        name: tool.name,
                        description: desc,
                        keywords: tool.keywords,
                        schema: schemaInfo
                    });
                case 'xml':
                    return `<tool name="${tool.name}" keywords="${(tool.keywords || []).join(', ')}">${desc}\n参数: ${schemaInfo}</tool>`;
                case 'list':
                default:
                    return `- ${tool.name}: ${desc}\n  参数: ${schemaInfo}`;
            }
        });

        return toolStrings.join('\n');
    }

    /**
     * 格式化工具的参数 schema
     */
    private formatToolSchema(tool: ITool): string {
        try {
            const shape = tool.schema.shape;
            const paramDescriptions: string[] = [];

            for (const [key, schema] of Object.entries(shape)) {
                const isOptional = schema._def.typeName === 'ZodOptional';
                const innerSchema = isOptional ? schema._def.innerType : schema;
                const description = innerSchema._def.description || key;
                const type = innerSchema._def.typeName === 'ZodString' ? 'string' :
                           innerSchema._def.typeName === 'ZodBoolean' ? 'boolean' :
                           innerSchema._def.typeName === 'ZodNumber' ? 'number' : 'unknown';

                paramDescriptions.push(`${key}${isOptional ? '?' : ''} (${type}): ${description}`);
            }

            return paramDescriptions.join(', ');
        } catch (error) {
            return '参数格式未知';
        }
    }

    /**
     * 格式化上下文提示
     */
    private formatContext(context: ConversationContext): string {
        let hint = `[上下文信息] 当前是第${context.turn + 1}轮对话。`;

        if (context.lastTool) {
            hint += ` 上一个工具是"${context.lastTool.name}"(执行${context.lastTool.success ? '成功' : '失败'})。`;
        }

        if (context.failedTools.size > 0) {
            hint += ` 失败过的工具有: ${Array.from(context.failedTools).join(', ')}。`;
        }

        return hint;
    }

    buildFallbackPrompt(
        query: string,
        history: IChatInputMessage[],
        thoughts: string[]
    ): IChatInputMessage[] {
        const historyStr = history.map(h => `${h.role}: ${h.content}`).join('\n');
        const thoughtsStr = thoughts.join('\n');

        // 检查是否有工具执行结果
        const hasToolResults = thoughts.some(thought => thought.includes('<observation>'));
        
        let instruction = '';
        if (hasToolResults) {
            instruction = `
            重要提示：在观察结果中包含了工具执行的数据。请仔细分析 <observation> 标签中的 JSON 数据，这些是工具成功执行后返回的结果。
            你应该基于这些工具执行结果来回答用户的问题，而不是说"抱歉"或"无法回答"。

            工具执行结果格式说明：
            - <observation>{"toolName":"工具名","success":true,"result":{...}}</observation>
            - 其中 result 字段包含了工具返回的实际数据
            - 请解析这些数据并基于数据内容生成有意义的回答
            `;
        }

        const fallbackContent = `
你已经为初始问题 "${query}" 执行了一系列思考和行动，但未能得出最终结论。
这是你的思考历史:
${historyStr}

这是你的内部思考和观察:
${thoughtsStr}
${instruction}

请根据以上所有信息，直接为用户生成一个最合理、最全面的最终答案。不要再使用任何工具，直接输出答案。
`;
        return [{ role: 'user', content: fallbackContent }];
    }
}
