/**
 * 用户角色枚举
 * 根据产品需求定义的4个核心角色
 */
export enum UserRole {
    /** 通用对话 - 处理日常对话、知识问答、创意写作等通用场景 */
    GENERAL_CHAT = 'general_chat',
    
    /** 研究员 - 深度研究和信息整合，提供全面的主题分析 */
    RESEARCHER = 'researcher',
    
    /** 行情分析师 - 提供专业的市场分析和投资洞察 */
    MARKET_ANALYST = 'market_analyst',
    
    /** 项目分析师 - 提供深度的项目分析和技术评估 */
    PROJECT_ANALYST = 'project_analyst',
}

/**
 * 角色配置接口
 */
export interface RoleConfig {
    /** 角色名称 */
    name: string;
    
    /** 角色描述 */
    description: string;
    
    /** 角色关键词列表 */
    keywords: string[];
    
    /** 推荐的工具组合 */
    recommendedTools: string[];
    
    /** 角色特定的提示词 */
    systemPrompt: string;
    
    /** 工作流程描述 */
    workflow: string[];
}

/**
 * 角色识别结果
 */
export interface RoleIdentificationResult {
    /** 识别的角色 */
    role: UserRole;
    
    /** 识别置信度 (0-1) */
    confidence: number;
    
    /** 匹配的关键词 */
    matchedKeywords: string[];
    
    /** 推荐的工具列表 */
    recommendedTools: string[];
    
    /** 角色特定的提示词 */
    rolePrompt: string;
}

/**
 * 角色路由选项
 */
export interface RoleRouteOptions {
    /** 是否启用角色识别 */
    enableRoleIdentification?: boolean;
    
    /** 强制指定角色 */
    forceRole?: UserRole;
    
    /** 角色识别的最低置信度阈值 */
    confidenceThreshold?: number;
    
    /** 是否使用角色特定的工具过滤 */
    useRoleToolFilter?: boolean;
}

/**
 * 扩展的路由结果，包含角色信息
 */
export interface ExtendedRouteResult {
    /** 识别的角色 */
    selectedRole: UserRole;
    
    /** 推荐的工具名称 */
    selectedTool: string | null;
    
    /** 角色识别结果 */
    roleIdentification: RoleIdentificationResult;
    
    /** 各阶段处理结果 */
    processingStages: {
        role?: RoleIdentificationResult;
        keyword?: any;
        vector?: any;
        voting?: any;
    };
    
    /** 处理时间 */
    processingTime: number;
    
    /** 是否命中缓存 */
    cacheHit: boolean;
    
    /** 是否使用了降级策略 */
    fallbackUsed?: boolean;
    
    /** 推荐置信度 */
    confidence: number;
    
    /** 角色特定的提示词 */
    rolePrompt: string;
}
