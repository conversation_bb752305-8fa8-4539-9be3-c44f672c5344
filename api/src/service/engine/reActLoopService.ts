import { Injectable, Logger } from '@nestjs/common';
import { PassThrough } from 'stream';
import { ITool } from '../../tools/tool.interface';
import { LlmService } from '../llm/llm.service';
import { ModelConfigService } from '../llm/modelConfig.service';
import { ToolExecutionEngine } from './toolExecutionEngine';
import { EventStreamEmitter } from './eventStreamEmitter';
import { PromptBuilder } from './promptBuilder';
import { ContextService } from './contextService';
import { IChatInputMessage } from '../../interface';
import { ToolExecutionTask } from '../../interface/concurrentExecution.interface';
import { randomUUID } from 'crypto';

/**
 * ReAct循环服务
 * 负责执行核心的ReAct(Reason-Action)推理循环
 */
@Injectable()
export class ReActLoopService {
    private readonly logger = new Logger(ReActLoopService.name);

    constructor(
        private readonly llmService: LlmService,
        private readonly modelConfigService: ModelConfigService,
        private readonly toolExecutor: ToolExecutionEngine,
        private readonly eventEmitter: EventStreamEmitter,
        private readonly promptBuilder: PromptBuilder,
        private readonly contextService: ContextService,
    ) {}

    /**
     * 执行ReAct循环
     * @returns 最终答案或null
     */
    async execute(
        query: string,
        history: IChatInputMessage[],
        tools: ITool[],
        stream: PassThrough,
        requestId: string,
        chatId: string,
        userId: string,
        maxTurns: number = 10,
    ): Promise<string | null> {
        const thoughts: string[] = [];
        let turns = 0;
        let lastCorrelationId: string | undefined;

        while (turns < maxTurns) {
            turns++;
            const correlationId = this.eventEmitter.generateCorrelationId();
            lastCorrelationId = correlationId;

            // 1. 获取上下文并构建Prompt
            const context = this.contextService.getOrCreate(userId, chatId);
            const messages = this.promptBuilder.build(query, history, tools, context, thoughts);

            this.eventEmitter.emitThought(stream, '开始新一轮思考...', turns, '', correlationId, requestId);

            // 2. 调用LLM获取思考和行动
            const llmResponse = await this.invokeLlm(messages, correlationId, requestId, stream);
            const parsedResponse = this.parseResponse(llmResponse);

            if (parsedResponse.thought) {
                thoughts.push(`<thought>${parsedResponse.thought}</thought>`);
                this.eventEmitter.emitThought(stream, parsedResponse.thought, turns, 'llm_reasoning', correlationId, requestId);
            }

            // 3. 检查是否有最终答案
            if (parsedResponse.final_answer) {
                this.eventEmitter.emitResponse(stream, parsedResponse.final_answer, correlationId, requestId, true);
                return parsedResponse.final_answer;
            }

            // 4. 执行行动
            if (parsedResponse.actions && parsedResponse.actions.length > 0) {
                thoughts.push(this.formatAction(parsedResponse.actions));

                const tasks: ToolExecutionTask[] = parsedResponse.actions.map((action, index) => ({
                    id: `${correlationId}-task-${index}`,
                    tool: tools.find(t => t.name === action.tool)!,
                    input: action.args,
                    dependencies: [],
                    status: 'pending',
                    retryCount: 0,
                    priority: 5,
                }));

                const results = await this.toolExecutor.execute(tasks, tools, stream, requestId, correlationId);

                // 处理执行结果并更新上下文
                results.forEach(result => {
                    thoughts.push(`<observation>${JSON.stringify(result)}</observation>`);
                    this.contextService.update(userId, chatId, {
                        toolName: result.toolName,
                        toolSuccess: result.success,
                        toolError: result.error,
                    });
                });
            } else {
                // 如果没有行动，可能是LLM困惑了
                this.logger.warn('[ReActLoop] LLM未生成行动，可能陷入困境');
                thoughts.push('<observation>没有检测到有效行动，请重新思考。</observation>');
            }
        }

        this.logger.warn(`[ReActLoop] 超过最大循环次数 ${maxTurns}，尝试生成最终答案`);
        try {
            const fallbackMessages = this.promptBuilder.buildFallbackPrompt(query, history, thoughts);
            const finalAnswer = await this.invokeLlm(fallbackMessages, lastCorrelationId || requestId, requestId, stream, 'final_answer');
            this.eventEmitter.emitResponse(stream, finalAnswer, lastCorrelationId || requestId, requestId, true);
            return finalAnswer;
        } catch (error: any) {
            this.logger.error(`[ReActLoop] 生成最终答案失败: ${error.message}`, error.stack);
            this.eventEmitter.emitError(stream, '生成最终答案失败', lastCorrelationId || requestId, requestId, 'FINAL_ANSWER_GENERATION_FAILED');
            return null;
        }
    }

    /**
     * 调用LLM
     */
    private async invokeLlm(
        messages: IChatInputMessage[],
        correlationId: string,
        requestId: string,
        stream: PassThrough,
        callType: 'reasoning' | 'final_answer' = 'reasoning'
    ): Promise<string> {
        const modelInfo = this.modelConfigService.selectProviderAndModel(
            callType === 'final_answer' ? 'complex' : 'medium'
        );
        if (!modelInfo) {
            throw new Error(`未配置模型 (场景: ${callType})`);
        }

        const callId = randomUUID();
        this.eventEmitter.emitLlmStart(stream, modelInfo.provider, modelInfo.model, callId, callType, correlationId, requestId);

        const startTime = Date.now();
        const response = await this.llmService.chat(modelInfo.provider, modelInfo.model, messages);
        const duration = Date.now() - startTime;

        this.eventEmitter.emitLlmEnd(stream, modelInfo.provider, modelInfo.model, callId, duration, 0, correlationId, requestId);

        return response.content;
    }

    /**
     * 解析LLM响应
     */
    private parseResponse(response: string): {
        thought?: string;
        actions?: Array<{ tool: string; args: any }>;
        final_answer?: string;
    } {
        // 添加调试日志，查看LLM的原始输出
        this.logger.debug(`[ReActLoop] LLM原始响应: ${response.substring(0, 500)}...`);
        
        const thoughtMatch = response.match(/<thought>([\s\S]*?)<\/thought>/);
        const finalAnswerMatch = response.match(/<final_answer>([\s\S]*?)<\/final_answer>/);

        if (finalAnswerMatch) {
            this.logger.debug(`[ReActLoop] 检测到最终答案: ${finalAnswerMatch[1].substring(0, 200)}...`);
            return { thought: thoughtMatch?.[1].trim(), final_answer: finalAnswerMatch[1].trim() };
        }

        const actionRegex = /<action\s+tool="([^"]+)"\s+args='([^']*)'\s*\/>/g;
        let match;
        const actions: Array<{ tool: string; args: any }> = [];

        while ((match = actionRegex.exec(response)) !== null) {
            try {
                this.logger.debug(`[ReActLoop] 检测到action: tool=${match[1]}, args=${match[2]}`);
                actions.push({
                    tool: match[1],
                    args: JSON.parse(match[2]),
                });
            } catch (error) {
                this.logger.warn(`[ReActLoop] 解析action参数失败: ${match[2]}`);
            }
        }

        if (actions.length === 0) {
            this.logger.warn(`[ReActLoop] 未检测到任何action，LLM可能没有按照正确格式输出`);
        }

        return {
            thought: thoughtMatch?.[1].trim(),
            actions: actions.length > 0 ? actions : undefined,
        };
    }

    /**
     * 格式化Action为字符串
     */
    private formatAction(actions: Array<{ tool: string; args: any }>): string {
        return actions.map(a => `<action tool="${a.tool}" args='${JSON.stringify(a.args)}' />`).join('\n');
    }
}
