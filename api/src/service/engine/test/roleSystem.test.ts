import { Test, TestingModule } from '@nestjs/testing';
import { RoleConfigManager } from '../roleConfigManager';
import { RoleIdentificationStrategy } from '../strategies/roleIdentificationStrategy';
import { PromptBuilder } from '../promptBuilder';
import { UserRole } from '../types/role.types';

describe('角色系统测试', () => {
    let roleConfigManager: RoleConfigManager;
    let roleIdentificationStrategy: RoleIdentificationStrategy;
    let promptBuilder: PromptBuilder;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RoleConfigManager,
                RoleIdentificationStrategy,
                PromptBuilder,
            ],
        }).compile();

        roleConfigManager = module.get<RoleConfigManager>(RoleConfigManager);
        roleIdentificationStrategy = module.get<RoleIdentificationStrategy>(RoleIdentificationStrategy);
        promptBuilder = module.get<PromptBuilder>(PromptBuilder);
    });

    describe('角色配置管理器', () => {
        it('应该正确加载所有角色配置', () => {
            const allConfigs = roleConfigManager.getAllRoleConfigs();
            expect(allConfigs.size).toBe(4);
            
            // 检查每个角色都有配置
            expect(allConfigs.has(UserRole.GENERAL_CHAT)).toBe(true);
            expect(allConfigs.has(UserRole.RESEARCHER)).toBe(true);
            expect(allConfigs.has(UserRole.MARKET_ANALYST)).toBe(true);
            expect(allConfigs.has(UserRole.PROJECT_ANALYST)).toBe(true);
        });

        it('应该正确返回角色配置', () => {
            const researcherConfig = roleConfigManager.getRoleConfig(UserRole.RESEARCHER);
            expect(researcherConfig).toBeDefined();
            expect(researcherConfig!.name).toBe('研究员');
            expect(researcherConfig!.keywords).toContain('研究');
            expect(researcherConfig!.recommendedTools).toContain('WebSearchTool');
        });

        it('应该正确匹配关键词', () => {
            const matches = roleConfigManager.matchRoleByKeywords('帮我研究一下比特币的最新动态');
            expect(matches.length).toBeGreaterThan(0);
            expect(matches[0].role).toBe(UserRole.RESEARCHER);
            expect(matches[0].matchedKeywords).toContain('研究');
        });
    });

    describe('角色识别策略', () => {
        it('应该正确识别研究员角色', () => {
            const query = '请帮我调研一下人工智能的发展趋势';
            const result = roleIdentificationStrategy.identify(query);

            expect(result.role).toBe(UserRole.RESEARCHER);
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.matchedKeywords).toContain('调研');
            expect(result.recommendedTools).toContain('WebSearchTool');
        });

        it('应该正确识别行情分析师角色', () => {
            const query = '比特币现在的价格是多少，未来走势如何？';
            const result = roleIdentificationStrategy.identify(query, { confidenceThreshold: 0.1 });

            expect(result.role).toBe(UserRole.MARKET_ANALYST);
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.matchedKeywords).toContain('价格');
        });

        it('应该正确识别项目分析师角色', () => {
            const query = '帮我分析一下Uniswap这个项目的技术和团队';
            const result = roleIdentificationStrategy.identify(query);
            
            expect(result.role).toBe(UserRole.PROJECT_ANALYST);
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.matchedKeywords).toContain('项目');
        });

        it('应该默认使用通用对话角色', () => {
            const query = '你好，今天天气怎么样？';
            const result = roleIdentificationStrategy.identify(query);
            
            expect(result.role).toBe(UserRole.GENERAL_CHAT);
        });

        it('应该支持强制指定角色', () => {
            const query = '随便什么内容';
            const result = roleIdentificationStrategy.identify(query, { 
                forceRole: UserRole.MARKET_ANALYST 
            });
            
            expect(result.role).toBe(UserRole.MARKET_ANALYST);
            expect(result.confidence).toBe(1.0);
        });
    });

    describe('提示词构建器', () => {
        it('应该为不同角色构建不同的提示词', () => {
            const researcherPrompt = promptBuilder.buildRolePrompt(UserRole.RESEARCHER);
            const analystPrompt = promptBuilder.buildRolePrompt(UserRole.MARKET_ANALYST);
            
            expect(researcherPrompt).toContain('研究员');
            expect(researcherPrompt).toContain('深度调研');
            expect(analystPrompt).toContain('行情分析师');
            expect(analystPrompt).toContain('市场分析');
            
            // 确保不同角色的提示词不同
            expect(researcherPrompt).not.toBe(analystPrompt);
        });

        it('应该支持自定义提示词', () => {
            const customPrompt = '这是一个自定义提示词';
            const result = promptBuilder.buildRolePrompt(UserRole.RESEARCHER, customPrompt);
            
            expect(result).toBe(customPrompt);
        });

        it('应该在没有角色时返回默认提示词', () => {
            const defaultPrompt = promptBuilder.buildRolePrompt();
            
            expect(defaultPrompt).toContain('智能的AI助手');
        });
    });

    describe('角色系统集成测试', () => {
        it('应该完整支持角色识别到提示词构建的流程', () => {
            const query = '请帮我调研一下最近人工智能的发展趋势';

            // 1. 角色识别
            const roleResult = roleIdentificationStrategy.identify(query);
            expect(roleResult.role).toBe(UserRole.RESEARCHER);
            
            // 2. 获取角色配置
            const roleConfig = roleConfigManager.getRoleConfig(roleResult.role);
            expect(roleConfig).toBeDefined();
            expect(roleConfig!.recommendedTools).toContain('WebSearchTool');

            // 3. 构建角色提示词
            const rolePrompt = promptBuilder.buildRolePrompt(roleResult.role);
            expect(rolePrompt).toContain('研究员');
            expect(rolePrompt).toContain('系统性思维');

            // 4. 验证工作流程
            expect(roleConfig!.workflow).toContain('理解研究主题和范围');
            expect(roleConfig!.workflow).toContain('搜索相关信息和最新动态');
        });

        it('应该正确处理边界情况', () => {
            // 空查询
            const emptyResult = roleIdentificationStrategy.identify('');
            expect(emptyResult.role).toBe(UserRole.GENERAL_CHAT);
            
            // 非常短的查询
            const shortResult = roleIdentificationStrategy.identify('hi');
            expect(shortResult.role).toBe(UserRole.GENERAL_CHAT);
            
            // 混合关键词
            const mixedResult = roleIdentificationStrategy.identify('研究比特币价格项目分析');
            expect(mixedResult.role).toBeOneOf([
                UserRole.RESEARCHER, 
                UserRole.MARKET_ANALYST, 
                UserRole.PROJECT_ANALYST
            ]);
        });
    });
});

// 自定义匹配器
expect.extend({
    toBeOneOf(received, expected) {
        const pass = expected.includes(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be one of ${expected}`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be one of ${expected}`,
                pass: false,
            };
        }
    },
});

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace jest {
        interface Matchers<R> {
            toBeOneOf(expected: any[]): R;
        }
    }
}
