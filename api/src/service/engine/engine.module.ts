import { Module, Global } from '@nestjs/common';
import { AgentExecutionFacade } from './agentExecutionFacade';
import { CacheService } from './cacheService';
import { ContextService } from './contextService';
import { EventStreamEmitter } from './eventStreamEmitter';
import { IntentRouterFacade } from './intentRouterFacade.service';
import { MetricsCollector } from './metricsCollector';
import { PromptBuilder } from './promptBuilder';
import { ReActLoopService } from './reActLoopService';
import { ToolRegistryService } from './toolRegistry.service';
import { KeywordFilterStrategy } from './strategies/keywordFilterStrategy';
import { LlmVotingStrategy } from './strategies/llmVotingStrategy';
import { VectorReducerStrategy } from './strategies/vectorReducerStrategy';
import { RoleIdentificationStrategy } from './strategies/roleIdentificationStrategy';
import { RoleConfigManager } from './roleConfigManager';
import { RoleWorkflowService } from './workflows/roleWorkflowService';
import { ToolExecutionEngine } from './toolExecutionEngine';
import { ToolsModule } from '../../tools/tools.module';
import { ModelProviderModule } from '../../module/modelProvider.module';
import { EmbeddingService } from '../llm/embedding.service';
import { PerformanceMonitorService } from '../llm/performanceMonitor.service';

@Global()
@Module({
    imports: [
        ToolsModule,
        ModelProviderModule,
    ],
    providers: [
        // Facades
        AgentExecutionFacade,
        IntentRouterFacade,

        // Core Services
        ReActLoopService,
        ToolExecutionEngine,
        ContextService,
        CacheService,

        // Role Management
        RoleConfigManager,
        RoleWorkflowService,

        // Strategies
        KeywordFilterStrategy,
        VectorReducerStrategy,
        LlmVotingStrategy,
        RoleIdentificationStrategy,

        // Utility Services
        EventStreamEmitter,
        MetricsCollector,
        PromptBuilder,
        EmbeddingService,
        PerformanceMonitorService,
    ],
    exports: [
        AgentExecutionFacade,
        IntentRouterFacade,
        RoleConfigManager,
        RoleWorkflowService,
        EventStreamEmitter,
        CacheService,
        MetricsCollector,
        EmbeddingService,
        PerformanceMonitorService,
    ],
})
export class EngineModule {}
