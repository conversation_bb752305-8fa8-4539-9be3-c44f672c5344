import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { ITool } from './tool.interface';
import { LlmService } from '../service/llm/llm.service';
import { IChatInputMessage } from '../interface';
import { LoaderFactory } from '../loaders/loader.factory';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DocumentEntity } from '../entities/document.entity';

@Injectable()
export class DocumentAnalysisTool implements ITool {
  private readonly logger = new Logger(DocumentAnalysisTool.name);

  constructor(
    private readonly llmService: LlmService,
    @InjectRepository(DocumentEntity)
    private readonly documentRepository: Repository<DocumentEntity>,
  ) {}

  name = 'document_analysis';

  keywords = [
    'document_analysis',
    'mcpTool.document_analysis',
    '文档分析',
    '文档内容',
    '分析文档',
    '分析总结文档',
    '分析一下文档',
    '分析一下文档内容',
    '查看文档',
    '读取文档',
    '解读文档',
    '解读文档内容',
    '分析文档内容',
  ];
  
  description =
    '分析用户上传的文档内容并回答相关问题。当用户上传了文档文件（如PDF、TXT等）并询问文档内容时使用此工具。工具会自动从用户消息中提取文档URL。';

  schema = z.object({
    query: z.string().describe('用户提出的关于文档内容的具体问题'),
  });

  async execute(input: { query: string }): Promise<any> {
    try {
      this.logger.log(`开始分析文档，问题: ${input.query}`);

      // 从数据库获取最新的文档URL
      const documentUrl = await this.getLatestDocumentUrl();
      
      if (!documentUrl) {
        return {
          error: '未找到文档URL，请确保已上传文档文件。',
          query: input.query,
          timestamp: Date.now(),
        };
      }

      // 获取文档内容
      const documentContent = await this.getDocumentContent(documentUrl);
      
      if (!documentContent) {
        return {
          error: '无法获取文档内容，请检查文档URL是否正确。',
          documentUrl: documentUrl,
          query: input.query,
          timestamp: Date.now(),
        };
      }

      // 构建提示词
      const prompt = `请根据以下文档内容回答用户问题。请提供详细、准确的分析。

【文档内容】
${documentContent}

【用户问题】
${input.query}

请基于文档内容进行分析和回答：`;

      // 构建消息
      const messages: IChatInputMessage[] = [
        {
          role: 'user',
          content: prompt
        }
      ];

      // 获取提供商和模型
      const { provider, model } = this.llmService.getProviderAndModel('complex');

      this.logger.log(`使用模型: ${provider}/${model} 进行文档分析`);

      // 调用大模型进行分析
      const response = await this.llmService.chat(provider, model, messages);

      if (!response || !response.content) {
        return {
          error: '大模型分析失败，请稍后重试。',
          documentUrl: documentUrl,
          query: input.query,
          timestamp: Date.now(),
        };
      }

      this.logger.log(`文档分析完成，响应长度: ${response.content.length}`);

      return {
        analysis: response.content,
        documentUrl: documentUrl,
        query: input.query,
        timestamp: Date.now(),
      };

    } catch (error: any) {
      this.logger.error(`文档分析失败: ${error.message}`, error.stack);
      return {
        error: `文档分析过程中发生错误: ${error.message}`,
        query: input.query,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 从数据库获取最新的文档URL
   */
  private async getLatestDocumentUrl(): Promise<string | null> {
    try {
      // 获取最新创建的文档
      const latestDocument = await this.documentRepository.findOne({
        where: {},
        order: { createdAt: 'DESC' },
      });

      if (!latestDocument) {
        this.logger.warn('数据库中未找到任何文档');
        return null;
      }

      this.logger.log(`找到最新文档: ${latestDocument.filename}, URL: ${latestDocument.url}`);
      return latestDocument.url;
    } catch (error: any) {
      this.logger.error(`获取最新文档URL失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 通过 LoaderFactory 获取文档内容
   */
  private async getDocumentContent(fileUrl: string): Promise<string | null> {
    try {
      this.logger.log(`尝试加载文档: ${fileUrl}`);
      
      // 根据文件扩展名判断MIME类型
      const mimeType = this.getMimeTypeFromUrl(fileUrl);
      this.logger.log(`根据URL判断MIME类型: ${mimeType}`);
      
      // 使用 LoaderFactory 加载文档内容
      const loader = LoaderFactory.getLoader(mimeType, fileUrl);
      const content = await loader.load();
      
      if (!content || content.trim().length === 0) {
        this.logger.error(`文档内容为空: ${fileUrl}`);
        return null;
      }

      this.logger.log(`成功加载文档内容: ${content.length} 字符`);
      return content;
    } catch (error: any) {
      this.logger.error(`获取文档内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 根据文件URL判断MIME类型
   */
  private getMimeTypeFromUrl(fileUrl: string): string {
    const url = fileUrl.toLowerCase();
    
    if (url.endsWith('.md') || url.endsWith('.markdown')) {
      return 'text/markdown';
    }
    if (url.endsWith('.txt')) {
      return 'text/plain';
    }
    if (url.endsWith('.pdf')) {
      return 'application/pdf';
    }
    if (url.endsWith('.jpg') || url.endsWith('.jpeg')) {
      return 'image/jpeg';
    }
    if (url.endsWith('.png')) {
      return 'image/png';
    }
    if (url.endsWith('.gif')) {
      return 'image/gif';
    }
    if (url.endsWith('.bmp')) {
      return 'image/bmp';
    }
    if (url.endsWith('.webp')) {
      return 'image/webp';
    }
    
    // 默认使用文本类型
    return 'text/plain';
  }
}
