import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { ITool } from './tool.interface';
import { SummarizeService } from '../service/research/summarize.service';
import { logger } from '../logger';

@Injectable()
export class SummarizeTextTool implements ITool {
    constructor(private readonly summarizeService: SummarizeService) {}

    name = 'summarize_text';

    keywords = [
        'summarize_text',
        'mcpTool.summarize_text',
        '总结',
        '总结文本',
        '总结内容',
        '总结文档',
        '总结文章',
        '总结报告',
        '总结论文',
        '总结书籍',
        '总结资料',
        '总结信息',
        '总结知识',
        '总结数据',
        '总结分析',
        '总结观点',
        '总结结论',
        '总结要点',
        '总结重点',
        '总结精华',
        '总结核心',
        '总结主要内容',
        '总结主要观点',
        '总结主要结论',
        '总结主要要点',
        '总结主要重点',
        '总结主要精华',
        '总结主要核心',
    ];
    description =
        "Summarizes a long piece of text with respect to a user's original query. Use this after fetching content from a URL to extract the most relevant information.";
    schema = z.object({
        text: z.string().describe('The text to be summarized.'),
        query: z.string().describe("The user's original query to focus the summary on."),
    });

    async execute({ text, query }: z.infer<typeof this.schema>): Promise<string> {
        try {
            logger.debug(`SummarizeTextTool: Summarizing text for query: "${query}"`);
            const summary = await this.summarizeService.summarize(text, query);
            logger.debug(`SummarizeTextTool: Summarization complete.`);
            return summary;
        } catch (error) {
            // logger.error(`SummarizeTextTool: Error during summarization`, { error });
            const errorMessage = error instanceof Error ? error.message : String(error);
            return `Error during summarization: ${errorMessage}`;
        }
    }
}
