import { Module, Global, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolRegistryService } from '../service/engine/toolRegistry.service';
import { ChatTool } from './chatTool';
import { MarketDataTool } from './marketData.tool';
import { ProjectDataTool } from './projectData.tool';
import { WebSearchTool } from './webSearch.tool';
import { ModelProviderModule } from '../module/modelProvider.module';
import { DocumentAnalysisTool } from './documentAnalysis.tool';
import { SearxngService } from '../service/integration/searxng.service';
import { JinaService } from '../service/integration/jina.service';
import { SummarizeService } from '../service/research/summarize.service';
import { FetchUrlTool } from './fetchUrl.tool';
import { SummarizeTextTool } from './summarizeText.tool';
import { LlmService } from '../service/llm/llm.service';
import { SearchService } from '../service/integration/search.service';
import { MarketDataService } from '../service/integration/marketData.service';
import { ProjectDataService } from '../service/research/projectData.service';
import { LlmMetricsService } from '../service/llm/llmMetrics.service';
import { DocumentModule } from '../module/document.module';
import { FileUploadModule } from '../module/fileUpload.module';
import { EmbeddingService } from '../service/llm/embedding.service';
import { DocumentEntity } from '../entities/document.entity';

@Global()
@Module({
  imports: [
    ModelProviderModule, 
    DocumentModule, 
    FileUploadModule,
    TypeOrmModule.forFeature([DocumentEntity])
  ],
  providers: [
    ToolRegistryService,
    ChatTool,
    MarketDataTool,
    ProjectDataTool,
    WebSearchTool,
    DocumentAnalysisTool,
    SearxngService,
    JinaService,
    SummarizeService,
    FetchUrlTool,
    SummarizeTextTool,
    LlmService,
    SearchService,
    MarketDataService,
    ProjectDataService,
    LlmMetricsService,
    EmbeddingService,
  ],
  exports: [ToolRegistryService, LlmService, LlmMetricsService],
})
export class ToolsModule implements OnModuleInit {
  constructor(
    private readonly registry: ToolRegistryService,
    private readonly chatTool: ChatTool,
    private readonly marketDataTool: MarketDataTool,
    private readonly projectDataTool: ProjectDataTool,
    private readonly webSearchTool: WebSearchTool,
    private readonly documentAnalysisTool: DocumentAnalysisTool,
    private readonly fetchUrlTool: FetchUrlTool,
    private readonly summarizeTextTool: SummarizeTextTool,
    private readonly llmService: LlmService,
    private readonly searchService: SearchService,
  ) {}

  async onModuleInit() {
    try {
      console.log('[ToolsModule] 开始注册工具...');
      
      console.log('[ToolsModule] 注册 ChatTool...');
      await this.registry.register(this.chatTool);
      
      console.log('[ToolsModule] 注册 MarketDataTool...');
      await this.registry.register(this.marketDataTool);
      
      console.log('[ToolsModule] 注册 ProjectDataTool...');
      await this.registry.register(this.projectDataTool);
      
      console.log('[ToolsModule] 注册 WebSearchTool...');
      await this.registry.register(this.webSearchTool);
      
      console.log('[ToolsModule] 注册 DocumentAnalysisTool...');
      await this.registry.register(this.documentAnalysisTool);
      
      console.log('[ToolsModule] 注册 FetchUrlTool...');
      await this.registry.register(this.fetchUrlTool);
      
      console.log('[ToolsModule] 注册 SummarizeTextTool...');
      await this.registry.register(this.summarizeTextTool);
      
      // 验证注册结果
      const allTools = this.registry.listTools();
      console.log('[ToolsModule] 工具注册完成，已注册工具列表:', allTools.map(t => t.name));
      
      // 特别检查 chat 工具
      const chatTool = this.registry.getTool('chat');
      console.log('[ToolsModule] Chat 工具检查:', chatTool ? '已注册' : '未注册');
      
    } catch (error) {
      console.error('[ToolsModule] 工具注册失败:', error);
      throw error;
    }
  }
}
