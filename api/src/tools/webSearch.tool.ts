import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { ITool } from './tool.interface';
import { SearxngService } from '../service/integration/searxng.service';
import { ISearchResponseResult } from '../interface';

@Injectable()
export class WebSearchTool implements ITool {
    private readonly logger = new Logger(WebSearchTool.name);

    constructor(private readonly searxngService: SearxngService) { }

    name = 'web_search';

    keywords = [
        'mcpTool.web_search',
        'web_search',
        '搜索',
        '查询',
        '搜索网络',
        '查询网络',
        '搜索互联网',
        '查询互联网',
    ];

    description =
        'When you need to answer questions about recent events, news, or any query that requires real-time information, use this tool to search the web.';

    schema = z.object({
        query: z
            .string()
            .describe('The keywords or question to search the web for.'),
    });

    async execute(input: { query: string }): Promise<ISearchResponseResult[] | { error: string; query: string; timestamp: number }> {
        try {
            // 输入验证
            if (!input.query || input.query.trim().length === 0) {
                throw new Error('搜索查询不能为空');
            }

            if (input.query.length > 500) {
                throw new Error('搜索查询过长，请限制在500字符以内');
            }

            this.logger.log(`[WebSearchTool] 开始搜索: "${input.query}"`);

            const searchResults = await this.searxngService.search({ q: input.query });

            if (!searchResults || searchResults.length === 0) {
                this.logger.warn(`[WebSearchTool] 未找到搜索结果: "${input.query}"`);
                return {
                    error: '未找到相关搜索结果，请尝试使用不同的关键词',
                    query: input.query,
                    timestamp: Date.now()
                };
            }

            this.logger.log(`[WebSearchTool] 搜索成功，找到 ${searchResults.length} 条结果`);

            // 仅返回前 8 条结构化结果
            return searchResults.slice(0, 8);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`[WebSearchTool] 搜索失败: ${errorMessage}`, error);
            
            // 返回结构化错误信息而不是空数组
            return {
                error: `网络搜索失败: ${errorMessage}`,
                query: input.query,
                timestamp: Date.now()
            };
        }
    }
} 