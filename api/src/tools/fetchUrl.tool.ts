import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { ITool } from './tool.interface';
import { JinaService } from '../service/integration/jina.service';
import { logger } from '../logger';

@Injectable()
export class FetchUrlTool implements ITool {
    constructor(private readonly jina: JinaService) {}

    name = 'fetch_url';

    keywords = [
        'fetch_url',
        'mcpTool.fetch_url',
        '获取网页内容',
        '获取网页文本',
        '获取网页信息',
        '获取网页数据',
        '获取网页',
        '抓取网页',
        '访问网页',
        '跳转网页',
        '跳转',
    ];
    description =
        'Fetches the main content of a given URL, returning it in markdown format. Useful for getting the full text of a webpage after a web search has identified a relevant link.';
    schema = z.object({
        url: z.string().url().describe('The URL of the webpage to fetch.'),
    });

    async execute({ url }: z.infer<typeof this.schema>): Promise<string> {
        try {
            logger.debug(`FetchUrlTool: Fetching content for URL: ${url}`);
            const results = await this.jina.jinaUrlsReader({ urls: [url], format: 'markdown' });
            if (results && results.length > 0 && results[0].content) {
                logger.debug(`FetchUrlTool: Successfully fetched content for URL: ${url}`);
                // Limit content to avoid overly large context windows
                return results[0].content.slice(0, 15000);
            }
            logger.warn(`FetchUrlTool: No content returned for URL: ${url}`);
            return 'Could not fetch content from the URL.';
        } catch (error) {
            logger.error(`FetchUrlTool: Error fetching URL ${url}`, { error });
            const errorMessage = error instanceof Error ? error.message : String(error);
            return `Error fetching URL: ${errorMessage}`;
        }
    }
} 