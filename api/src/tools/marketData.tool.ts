import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { ITool } from './tool.interface';
import { MarketDataService } from '../service/integration/marketData.service';

@Injectable()
export class MarketDataTool implements ITool {
    constructor(private readonly marketDataService: MarketDataService) {}

    name = 'get_market_data';

    description =
        '获取加密货币的市场行情数据，如价格、交易量、K线等。适用于查询特定代币（如BTC、ETH）的行情信息。支持实时价格、24小时变化、交易量等数据。';

    /**
     * 关键词用于 IntentRouterService 的预过滤。
     * 覆盖常见的中英文表达，确保"比特币行情"等中文问法能够命中该工具。
     */
    keywords = [
        'get_market_data',
        'mcpTool.get_market_data',
        '价格',
        '行情',
        '市值',
        '涨跌',
        '交易量',
        '多少钱',
        '值多少',
        '走势',
        '图表',
        'K线',
        '币价',
        '代币',
        'BTC',
        'ETH',
        '比特币',
        '比特币行情',
        'BTC行情',
        '比特币价格',
        'ETH价格',
        'ETH行情',
        'ETH价格',
        'ETH走势',
        '以太坊',
        '分析行情',
        '分析市场',
        '分析市场行情',
        '分析',
    ];

    schema = z.object({
        symbol: z.string().describe('要查询的加密货币的交易对符号，例如 "BTC", "ETH", "BTC/USDT"'),
        time_range: z
            .string()
            .optional()
            .describe('查询的时间范围，例如 "24h", "7d", "1M"，默认为24h'),
        include_kline: z.boolean().optional().describe('是否包含K线数据，默认为false'),
    });

    async execute(input: {
        symbol: string;
        time_range?: string;
        include_kline?: boolean;
    }): Promise<any> {
        try {
            const { symbol, time_range = '24h', include_kline = false } = input;

            // 智能判断：当用户查询市场数据时，通常需要K线图来展示价格走势
            // 除非明确指定不需要，否则默认包含K线数据以提供更完整的市场分析
            const shouldIncludeKline = include_kline !== false; // 只有明确设置为false时才不包含

            // 获取基础市场数据
            const marketData = await this.marketDataService.getMarketData(symbol);

            const result: any = {
                ...marketData,
                analysis: this.generateAnalysis(marketData),
            };

            // 如果需要K线数据
            if (shouldIncludeKline) {
                const klineData = await this.marketDataService.getKlineData(symbol, time_range);
                result.kline_data = klineData;
                result.chart_suggestion = 'k-line-chart';
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                error: `获取 ${input.symbol} 市场数据失败: ${errorMessage}`,
                symbol: input.symbol.toUpperCase(),
                timestamp: Date.now(),
            };
        }
    }

    private generateAnalysis(data: any): string {
        const { symbol, price, change_24h_percent } = data;

        let trend = '横盘整理';
        if (change_24h_percent > 5) {
            trend = '强势上涨';
        } else if (change_24h_percent > 2) {
            trend = '温和上涨';
        } else if (change_24h_percent < -5) {
            trend = '大幅下跌';
        } else if (change_24h_percent < -2) {
            trend = '温和下跌';
        }

        return `${symbol} 当前价格为 $${price.toFixed(2)}，24小时变化 ${change_24h_percent.toFixed(2)}%，呈现${trend}态势。`;
    }
}
