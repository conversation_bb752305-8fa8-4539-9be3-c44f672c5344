import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { ProjectDataService } from '../service/research/projectData.service';
import { ITool } from './tool.interface';

@Injectable()
export class ProjectDataTool implements ITool {
    constructor(private readonly projectDataService: ProjectDataService) {}

    name = 'get_project_data';

    keywords = [
        'get_project_data',
        'mcpTool.get_project_data',
        '搜索项目',
        '查询项目',
        '货币项目',
        '项目信息',
        '项目背景',
        '项目白皮书',
        '项目团队',
        '项目技术架构',
        '项目路线图',
        '项目发展历程',
        '项目发展现状',
        '项目怎么样',
        '项目如何',
        '项目',
        '介绍项目',
        '介绍一下项目',
    ];

    description =
        '获取加密货币项目的详细信息，如白皮书、团队背景、技术架构、路线图等。适用于查询特定项目（如 Uniswap, Solana）的背景资料。结合预设数据库和实时搜索结果。';

    schema = z.object({
        project_name: z
            .string()
            .describe('要查询的加密货币项目的名称，例如 "Uniswap", "Bitcoin", "Ethereum"'),
        include_search: z.boolean().optional().describe('是否包含最新搜索结果，默认为true'),
    });

    async execute(input: { project_name: string; include_search?: boolean }): Promise<any> {
        try {
            const { project_name, include_search = true } = input;

            // 获取项目信息
            const projectInfo = await this.projectDataService.getProjectInfo(project_name);

            // 添加分析和建议
            const result = {
                ...projectInfo,
                analysis: this.generateAnalysis(projectInfo),
                investment_perspective: this.generateInvestmentPerspective(projectInfo),
            };

            // 如果不需要搜索结果，移除相关字段
            if (!include_search) {
                delete result.search_results;
                delete result.additional_info;
                delete result.last_searched;
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                error: `获取 ${input.project_name} 项目数据失败: ${errorMessage}`,
                project_name: input.project_name,
                timestamp: Date.now(),
            };
        }
    }

    private generateAnalysis(projectInfo: any): string {
        const { name, category, key_features, founded } = projectInfo;

        let analysis = `${name} 是一个 ${category} 类型的项目`;

        if (founded) {
            const age = new Date().getFullYear() - parseInt(founded);
            analysis += `，成立于 ${founded} 年，已运行 ${age} 年`;
        }

        if (key_features && key_features.length > 0) {
            analysis += `。主要特点包括：${key_features.slice(0, 3).join('、')}`;
        }

        return analysis + '。';
    }

    private generateInvestmentPerspective(projectInfo: any): any {
        const { category, key_features, tokenomics, partnerships } = projectInfo;

        const perspective = {
            strengths: [] as string[],
            risks: [] as string[],
            market_position: '',
        };

        // 分析优势
        if (key_features) {
            perspective.strengths = key_features.slice(0, 3);
        }

        if (partnerships && partnerships.length > 0) {
            perspective.strengths.push(`与 ${partnerships.length} 个项目建立合作关系`);
        }

        // 分析风险
        if (category === 'Currency') {
            perspective.risks.push('监管风险');
            perspective.risks.push('市场波动风险');
        } else if (category === 'DeFi') {
            perspective.risks.push('智能合约风险');
            perspective.risks.push('流动性风险');
        }

        // 市场定位
        if (category === 'Smart Contract Platform') {
            perspective.market_position = '智能合约平台竞争激烈，需关注生态发展';
        } else if (category === 'DeFi') {
            perspective.market_position = 'DeFi 领域增长潜力大，但监管不确定性较高';
        } else {
            perspective.market_position = '需要根据具体项目特点进行评估';
        }

        return perspective;
    }
}
