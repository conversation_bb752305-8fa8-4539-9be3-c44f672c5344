import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrchestratorController } from '../controllers/chat.controller';
import { ChatSessionEntity, ChatStreamEntity, MessageAttachmentEntity } from '../entities/chatCore.entity';

import { ChatDataService } from '../service/chat/chatData.service';
import { ToolsModule } from '../tools/tools.module';
import { EngineModule } from '../service/engine/engine.module';
import { NestChatHistoryService } from '../service/chat/nestChatHistory.service';

@Module({
    imports: [
        ToolsModule,
        EngineModule,
        TypeOrmModule.forFeature([
            ChatStreamEntity,
            ChatSessionEntity,
            MessageAttachmentEntity,
        ]),
    ],
    controllers: [OrchestratorController],
    providers: [
        ChatDataService,
        NestChatHistoryService,
    ],
    exports: [
    ],
})
export class OrchestratorModule { }
