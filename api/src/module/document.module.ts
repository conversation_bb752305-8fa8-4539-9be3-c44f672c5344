import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentEntity } from '../entities/document.entity';
import { DocumentService } from '../service/infra/document.service';
import { DocumentController } from '../controllers/document.controller';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentEntity])],
  providers: [DocumentService],
  controllers: [DocumentController],
  exports: [DocumentService, TypeOrmModule],
})
export class DocumentModule {}
