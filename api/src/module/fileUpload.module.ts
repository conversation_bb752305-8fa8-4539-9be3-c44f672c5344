import { Modu<PERSON> } from '@nestjs/common';
import { FileUploadController } from '../controllers/fileUpload.controller';
import { FilesController } from '../controllers/files.controller';
import { FileUploadService } from '../service/infra/fileUpload.service';
import { FileStorageService } from '../service/infra/fileStorage.service';
import { SimpleQueueService } from '../service/infra/simpleQueue.service';
import { DocumentProcessingService } from '../service/research/documentProcessing.service';
import { DocumentModule } from './document.module';
// import { BullModule } from '@nestjs/bull';
// import { FileProcessingConsumer } from '../service/fileUpload.service';

@Module({
    imports: [DocumentModule], // BullModule.registerQueue({ name: 'file-processing' })],
    controllers: [FileUploadController, FilesController],
    providers: [
        FileUploadService,
        FileStorageService,
        SimpleQueueService,
        DocumentProcessingService,
        // FileProcessingConsumer
    ],
    exports: [DocumentProcessingService],
})
export class FileUploadModule { }
