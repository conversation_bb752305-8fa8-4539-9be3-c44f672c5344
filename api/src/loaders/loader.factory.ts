import { DocumentLoader } from './base.loader';
import { PDFLoader } from './pdf.loader';
import { MarkdownLoader } from './markdown.loader';
import { ImageOCRLoader } from './imageOcr.loader';
import { TextLoader } from './text.loader';

export class LoaderFactory {
  static getLoader(mimetype: string, url: string): DocumentLoader {
    // 根据文件扩展名判断文件类型
    const fileExtension = this.getFileExtension(url);
    
    switch (mimetype) {
      case 'application/pdf':
        return new PDFLoader(url);
      case 'text/markdown':
      case 'text/x-markdown':
      case 'application/x-markdown':
        return new MarkdownLoader(url);
      case 'image/jpeg':
      case 'image/jpg':
      case 'image/png':
      case 'image/gif':
      case 'image/bmp':
      case 'image/webp':
        return new ImageOCRLoader(url);
      case 'text/plain':
        return new TextLoader(url);
      // case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      //   return new DocxLoader(url);
      default:
        // 如果mimetype不支持，尝试根据扩展名判断，但只允许安全的文件类型
        return this.getLoaderByExtension(fileExtension, url, mimetype);
    }
  }

  /**
   * 根据文件扩展名获取加载器
   */
  private static getLoaderByExtension(extension: string, url: string, originalMimetype: string): DocumentLoader {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return new PDFLoader(url);
      case '.md':
      case '.markdown':
        return new MarkdownLoader(url);
      case '.txt':
        return new TextLoader(url);
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.bmp':
      case '.webp':
        return new ImageOCRLoader(url);
      default:
        // 对于未知扩展名，如果是text/plain或application/octet-stream，使用文本加载器
        if (originalMimetype === 'text/plain' || originalMimetype === 'application/octet-stream') {
          return new TextLoader(url);
        }
        throw new Error(`Unsupported file type: ${originalMimetype} with extension: ${extension}`);
    }
  }

  /**
   * 从URL中提取文件扩展名
   */
  private static getFileExtension(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const lastDotIndex = pathname.lastIndexOf('.');
      if (lastDotIndex !== -1) {
        return pathname.substring(lastDotIndex);
      }
      return '';
    } catch (error) {
      // 如果URL解析失败，尝试从字符串中提取扩展名
      const lastDotIndex = url.lastIndexOf('.');
      if (lastDotIndex !== -1) {
        return url.substring(lastDotIndex);
      }
      return '';
    }
  }

  /**
   * 获取支持的文件类型列表
   */
  static getSupportedMimeTypes(): string[] {
    return [
      'application/pdf',
      'text/markdown',
      'text/x-markdown', 
      'application/x-markdown',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
  }

  /**
   * 检查文件类型是否支持
   */
  static isSupported(mimetype: string): boolean {
    return this.getSupportedMimeTypes().includes(mimetype);
  }
} 