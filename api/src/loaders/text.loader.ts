import { DocumentLoader } from './base.loader';
import { httpRequest } from '../utils/http';
import { logger } from '../logger';

export class TextLoader implements DocumentLoader {
  constructor(private readonly fileUrl: string) {
  }

  async load(): Promise<string> {
    try {
      logger.info('开始加载文本文件', { fileUrl: this.fileUrl });
      
      const response = await httpRequest({
        endpoint: this.fileUrl,
        method: 'GET',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.statusText}`);
      }
      
      const content = await response.text();
      
      logger.info('文本文件加载成功', { 
        fileUrl: this.fileUrl, 
        contentLength: content.length 
      });
      
      return content;
    } catch (error: any) {
      logger.error('Error loading text file:', error);
      throw new Error(`Failed to load text file: ${error.message}`);
    }
  }
} 