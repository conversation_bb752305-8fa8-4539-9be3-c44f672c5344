{"name": "@ai-server/api", "private": true, "main": "./dist/app.js", "author": "zac_ma", "version": "1.1.1", "license": "MIT", "packageManager": "bun@1.0.0", "engines": {"node": ">=20"}, "scripts": {"prebuild": "bunx rim<PERSON>f dist", "build": "bunx nest build", "format": "bunx prettier --write \"src/**/*.ts\"", "start": "bunx nest start", "dev": "bun scripts/start-api-if-needed.ts", "start:debug": "bunx nest start --debug --watch", "start:prod": "node dist/main", "lint": "bunx eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "bunx jest", "test:watch": "bunx jest --watch", "test:cov": "bunx jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "bunx jest --config ./test/jest-e2e.json"}, "dependencies": {"@ai-sdk/openai-compatible": "^0.1.11", "@dotenvx/dotenvx": "^0.24.0", "@google/generative-ai": "^0.21.0", "@langchain/core": "^0.3.55", "@lmstudio/sdk": "^0.4.7", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.3.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.3.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@tavily/core": "^0.0.3", "@types/html-to-text": "^9.0.4", "@types/markdown-it": "^14.1.2", "@types/multer": "^1.4.13", "@vercel/blob": "^1.1.1", "ai": "^4.1.44", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cache-manager": "^5.4.0", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compute-cosine-similarity": "^1.1.0", "dotenv": "^16.4.5", "express": "^4.18.2", "fetch-sse": "^1.1.1", "gray-matter": "^4.0.3", "html-to-text": "^9.0.5", "install": "^0.13.0", "joi": "^17.13.3", "langchain": "^0.3.25", "lru-cache": "^11.1.0", "markdown-it": "^14.1.0", "ollama": "^0.5.12", "openai": "^5.3.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pg": "^8.16.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "tencentcloud-sdk-nodejs": "^4.0.1021", "tesseract.js": "^6.0.1", "testcontainers": "^10.25.0", "typeorm": "^0.3.24", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.56"}, "devDependencies": {"@darraghor/eslint-plugin-nestjs-typed": "^6.6.4", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^8.57.0", "@nestjs/cli": "^10.3.0", "@nestjs/testing": "^11.1.3", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^3.0.1", "@types/node": "^24.0.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "cross-env": "^7.0.3", "esbuild": "0.18.0", "eslint": "8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nodemon": "^3.1.0", "prettier": "^3.5.3", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "ts-prune": "^0.10.3", "tsx": "^4.20.1", "typescript": "^5.8.3"}}