# ---- 阶段 1: 使用预构建的依赖基础镜像 ----
FROM coinflow/api-deps:latest AS deps

# ---- 阶段 2: Builder ----
FROM coinflow/bun-base as builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN bun run build

# ---- 阶段 3: Production Runner ----
FROM coinflow/bun-base
ENV NODE_ENV=production
WORKDIR /app

# 从 deps 阶段复制已安装的依赖
COPY --from=deps /app/node_modules ./node_modules

# 从 builder 阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 复制 package.json（某些库运行时需要）
COPY --from=builder /app/package.json ./package.json

EXPOSE 3001
CMD ["bun", "run", "start:prod"]
